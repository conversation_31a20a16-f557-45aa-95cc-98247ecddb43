{"name": "@react-native/core-cli-utils", "version": "0.83.0-main", "description": "React Native CLI library for Frameworks to build on", "license": "MIT", "main": "./src/index.flow.js", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/core-cli-utils"}, "exports": {".": "./src/index.js", "./package.json": "./package.json", "./version.js": "./src/public/version.js"}, "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/core-cli-utils#readme", "keywords": ["cli-utils", "react-native"], "bugs": "https://github.com/facebook/react-native/issues", "engines": {"node": ">= 20.19.4"}, "files": ["dist"], "dependencies": {}, "devDependencies": {}}