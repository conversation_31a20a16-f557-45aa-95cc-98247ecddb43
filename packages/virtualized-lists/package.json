{"name": "@react-native/virtualized-lists", "version": "0.83.0-main", "description": "Virtualized lists for React Native.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/virtualized-lists"}, "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/virtualized-lists#readme", "keywords": ["lists", "virtualized-lists", "section-lists", "react-native"], "bugs": "https://github.com/facebook/react-native/issues", "engines": {"node": ">= 20.19.4"}, "exports": {".": {"react-native-strict-api": "./types_generated/index.d.ts", "types": "./index.d.ts", "default": "./index.js"}, "./*": {"types": null, "default": "./*.js"}, "./package.json": "./package.json"}, "files": ["index.js", "index.d.ts", "Lists", "README.md", "types_generated", "Utilities", "!**/__docs__/**", "!**/__fixtures__/**", "!**/__mocks__/**", "!**/__tests__/**"], "dependencies": {"invariant": "^2.2.4", "nullthrows": "^1.1.1"}, "devDependencies": {"react-test-renderer": "19.1.1"}, "peerDependencies": {"@types/react": "^19.1.1", "react": "*", "react-native": "*"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}