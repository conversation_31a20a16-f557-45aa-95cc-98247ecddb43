{"name": "@react-native/debugger-shell", "productName": "React Native DevTools", "version": "0.83.0-main", "description": "Experimental debugger shell for React Native for use with @react-native/debugger-frontend", "keywords": ["react-native", "tools"], "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/debugger-shell#readme", "bugs": "https://github.com/facebook/react-native/issues", "main": "./src/index.js", "exports": {".": {"node": "./src/node/index.js", "electron": "./src/electron/index.js"}, "./package.json": "./package.json"}, "scripts": {"dev": "electron src/electron"}, "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/debugger-shell"}, "license": "MIT", "engines": {"node": ">= 20.19.4"}, "dependencies": {"cross-spawn": "^7.0.6", "fb-dotslash": "0.5.8"}, "devDependencies": {"electron": "37.2.6", "semver": "^7.1.3"}, "files": ["!**/__tests__/**", "bin", "dist", "!src/electron"]}