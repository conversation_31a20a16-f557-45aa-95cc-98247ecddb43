{"name": "@react-native/dev-middleware", "version": "0.83.0-main", "description": "Dev server middleware for React Native", "keywords": ["react-native", "tools"], "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/dev-middleware#readme", "bugs": "https://github.com/facebook/react-native/issues", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/dev-middleware"}, "license": "MIT", "exports": {".": "./src/index.js", "./package.json": "./package.json"}, "files": ["dist"], "dependencies": {"@isaacs/ttlcache": "^1.4.1", "@react-native/debugger-frontend": "0.83.0-main", "@react-native/debugger-shell": "0.83.0-main", "chrome-launcher": "^0.15.2", "chromium-edge-launcher": "^0.2.0", "connect": "^3.6.5", "debug": "^4.4.0", "invariant": "^2.2.4", "nullthrows": "^1.1.1", "open": "^7.0.3", "serve-static": "^1.16.2", "ws": "^7.5.10"}, "engines": {"node": ">= 20.19.4"}, "devDependencies": {"@react-native/debugger-shell": "0.83.0-main", "selfsigned": "^2.4.1", "undici": "^5.29.0", "wait-for-expect": "^3.0.2"}}