{"name": "@react-native/metro-config", "version": "0.83.0-main", "description": "Metro configuration for React Native.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/metro-config"}, "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/metro-config#readme", "keywords": ["metro", "config", "react-native"], "bugs": "https://github.com/facebook/react-native/issues", "engines": {"node": ">= 20.19.4"}, "exports": {".": "./src/index.js", "./package.json": "./package.json"}, "files": ["dist"], "dependencies": {"@react-native/js-polyfills": "0.83.0-main", "@react-native/metro-babel-transformer": "0.83.0-main", "metro-config": "^0.83.3", "metro-runtime": "^0.83.3"}}