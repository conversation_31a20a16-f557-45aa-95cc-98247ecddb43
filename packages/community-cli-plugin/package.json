{"name": "@react-native/community-cli-plugin", "version": "0.83.0-main", "description": "Core CLI commands for React Native", "keywords": ["react-native", "tools"], "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/community-cli-plugin#readme", "bugs": "https://github.com/facebook/react-native/issues", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/community-cli-plugin"}, "license": "MIT", "exports": {".": "./src/index.js", "./package.json": "./package.json"}, "files": ["dist"], "dependencies": {"@react-native/dev-middleware": "0.83.0-main", "debug": "^4.4.0", "invariant": "^2.2.4", "metro": "^0.83.3", "metro-config": "^0.83.3", "metro-core": "^0.83.3", "semver": "^7.1.3"}, "devDependencies": {"metro-resolver": "^0.83.3"}, "peerDependencies": {"@react-native-community/cli": "*", "@react-native/metro-config": "*"}, "peerDependenciesMeta": {"@react-native-community/cli": {"optional": true}, "@react-native/metro-config": {"optional": true}}, "engines": {"node": ">= 20.19.4"}}