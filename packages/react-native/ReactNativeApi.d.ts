/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<a30dc676366600c08cf526052c4bbb0e>>
 *
 * This file was generated by scripts/js-api/build-types/index.js.
 */

// ----------------------------------------------------------------------------
// JavaScript API snapshot for react-native.
//
// This snapshot captures the public JavaScript API of React Native, based on
// types exported by packages/react-native/index.js.flow.
//
// Modifications to this file indicate changes to the shape of JavaScript
// values and types that can be imported from the react-native package.
// ----------------------------------------------------------------------------

/* eslint-disable redundant-undefined/redundant-undefined */

import * as React from "react"
declare const $$AndroidSwitchNativeComponent: NativeType
declare const $$AnimatedFlatList: <ItemT = any>(
  props: Omit<AnimatedProps<FlatListProps<ItemT>>, "ref"> & {
    ref?: React.Ref<FlatList<ItemT>>
  },
) => React.ReactNode
declare const $$AnimatedImage: AnimatedComponentType<
  React.JSX.LibraryManagedAttributes<
    typeof Image,
    React.ComponentProps<typeof Image>
  >,
  React.ComponentRef<typeof Image>
>
declare const $$AnimatedImplementation: {
  add: typeof addImpl
  attachNativeEvent: typeof attachNativeEventImpl
  Color: typeof AnimatedColor_default
  createAnimatedComponent: typeof createAnimatedComponent_default
  decay: typeof decayImpl
  delay: typeof delayImpl
  diffClamp: typeof diffClampImpl
  divide: typeof divideImpl
  event: typeof eventImpl
  Event: typeof AnimatedEvent
  forkEvent: typeof forkEventImpl
  Interpolation: typeof AnimatedInterpolation_default
  loop: typeof loopImpl
  modulo: typeof moduloImpl
  multiply: typeof multiplyImpl
  Node: typeof AnimatedNode_default
  parallel: typeof parallelImpl
  sequence: typeof sequenceImpl
  spring: typeof springImpl
  stagger: typeof staggerImpl
  subtract: typeof subtractImpl
  timing: typeof timingImpl
  unforkEvent: typeof unforkEventImpl
  Value: typeof AnimatedValue_default
  ValueXY: typeof AnimatedValueXY_default
}
declare const $$AnimatedScrollView: typeof AnimatedScrollView_default
declare const $$AnimatedSectionList: <ItemT = any, SectionT = any>(
  props: Omit<AnimatedProps<SectionListProps<ItemT, SectionT>>, "ref"> & {
    ref?: React.Ref<SectionList<ItemT, SectionT>>
  },
) => React.ReactNode
declare const $$AnimatedText: AnimatedComponentType<
  TextProps,
  React.ComponentRef<typeof Text>
>
declare const $$AnimatedView: AnimatedComponentType<
  ViewProps,
  React.ComponentRef<typeof View>
>
declare const $$flattenStyle: typeof flattenStyle_default
declare const $$index: {
  keyExtractor: typeof keyExtractor
  get FillRateHelper(): FillRateHelperT
  get ViewabilityHelper(): ViewabilityHelperT
  get VirtualizedList(): VirtualizedListT
  get VirtualizedListContextResetter(): VirtualizedListContextResetterT
  get VirtualizedSectionList(): AnyVirtualizedSectionList
}
declare const $$NativeDeviceInfo: typeof NativeDeviceInfo_default
declare const $$NativeDialogManagerAndroid: null | Spec | undefined
declare const $$ProgressBarAndroidNativeComponent: HostComponent<AndroidProgressBarNativeProps>
declare const $$ScrollViewContext: typeof ScrollViewContext_default
declare const $$ScrollViewNativeComponent: typeof ScrollViewNativeComponent_default
declare const $$SwitchNativeComponent: ComponentType
declare const $$ViewNativeComponent: typeof ViewNativeComponent_default
declare const absoluteFill: AbsoluteFillStyle
declare const absoluteFillObject: AbsoluteFillStyle
declare const AccessibilityInfo: typeof AccessibilityInfo_default
declare const AccessibilityInfo_default: {
  addEventListener<K extends keyof AccessibilityEventDefinitions>(
    eventName: K,
    handler: (...$$REST$$: AccessibilityEventDefinitions[K]) => void,
  ): EventSubscription
  announceForAccessibility(announcement: string): void
  announceForAccessibilityWithOptions(
    announcement: string,
    options: {
      queue?: boolean
    },
  ): void
  getRecommendedTimeoutMillis(originalTimeout: number): Promise<number>
  isAccessibilityServiceEnabled(): Promise<boolean>
  isBoldTextEnabled(): Promise<boolean>
  isDarkerSystemColorsEnabled(): Promise<boolean>
  isGrayscaleEnabled(): Promise<boolean>
  isHighTextContrastEnabled(): Promise<boolean>
  isInvertColorsEnabled(): Promise<boolean>
  isReduceMotionEnabled(): Promise<boolean>
  isReduceTransparencyEnabled(): Promise<boolean>
  isScreenReaderEnabled(): Promise<boolean>
  prefersCrossFadeTransitions(): Promise<boolean>
  sendAccessibilityEvent(
    handle: HostInstance,
    eventType: AccessibilityEventTypes,
  ): void
  setAccessibilityFocus(reactTag: number): void
}
declare const ActionSheetIOS: typeof ActionSheetIOS_default
declare const ActionSheetIOS_default: {
  dismissActionSheet: () => void
  showActionSheetWithOptions(
    options: ActionSheetIOSOptions,
    callback: (buttonIndex: number) => void,
  ): void
  showShareActionSheetWithOptions(
    options: ShareActionSheetIOSOptions,
    failureCallback: ((error: ShareActionSheetError) => void) | Function,
    successCallback:
      | ((success: boolean, method: null | string | undefined) => void)
      | Function,
  ): void
}
declare const ActivityIndicator: typeof ActivityIndicator_default
declare const ActivityIndicator_default: (
  props: ActivityIndicatorProps & {
    ref?: React.Ref<HostComponent<never>>
  },
) => React.ReactNode
declare const add: typeof $$AnimatedImplementation.add
declare const addImpl: (
  a: AnimatedNode_default | number,
  b: AnimatedNode_default | number,
) => AnimatedAddition_default
declare const AnimatedScrollView_default: AnimatedComponentType<
  ScrollViewProps,
  AnimatedScrollViewInstance
>
declare const AppState: typeof AppState_default
declare const AppState_default: AppStateImpl
declare const attachNativeEvent: typeof $$AnimatedImplementation.attachNativeEvent
declare const BackHandler: typeof BackHandler_default
declare const BackHandler_default: TBackHandler
declare const Button: typeof Button_default
declare const Button_default: (
  props: ButtonProps & {
    ref?: React.Ref<ButtonRef>
  },
) => React.ReactNode
declare const Clipboard: {
  getString(): Promise<string>
  setString(content: string): void
}
declare const codegenNativeCommands: typeof codegenNativeCommands_default
declare const codegenNativeComponent: typeof codegenNativeComponent_default
declare const compose: typeof composeStyles_default
declare const create: <S extends ____Styles_Internal>(
  obj: S & ____Styles_Internal,
) => Readonly<S>
declare const createAnimatedComponent: typeof $$AnimatedImplementation.createAnimatedComponent
declare const decay: typeof $$AnimatedImplementation.decay
declare const decayImpl: (
  value:
    | AnimatedColor_default
    | AnimatedValue_default
    | AnimatedValueXY_default,
  config: DecayAnimationConfig,
) => CompositeAnimation
declare const delay: typeof $$AnimatedImplementation.delay
declare const delayImpl: (time: number) => CompositeAnimation
declare const DeviceEventEmitter: IEventEmitter<RCTDeviceEventDefinitions>
declare const DeviceInfo: typeof $$NativeDeviceInfo
declare const DevMenu: typeof DevMenu_default
declare const DevMenu_default: DevMenuStatic
declare const DevSettings: typeof DevSettings_default
declare let DevSettings_default: {
  addMenuItem(title: string, handler: () => unknown): void
  onFastRefresh(): void
  reload(reason?: string): void
}
declare const diffClamp: typeof $$AnimatedImplementation.diffClamp
declare const diffClampImpl: (
  a: AnimatedNode_default,
  min: number,
  max: number,
) => AnimatedDiffClamp_default
declare const divide: typeof $$AnimatedImplementation.divide
declare const divideImpl: (
  a: AnimatedNode_default | number,
  b: AnimatedNode_default | number,
) => AnimatedDivision_default
declare const DrawerLayoutAndroid: typeof DrawerLayoutAndroid_default
declare const DynamicColorIOS: (tuple: DynamicColorIOSTuple) => ColorValue
declare const Easing: typeof EasingStatic_default
declare const EasingStatic_default: {
  back(s?: number): EasingFunction
  bezier(x1: number, y1: number, x2: number, y2: number): EasingFunction
  bounce(t: number): number
  circle(t: number): number
  cubic(t: number): number
  ease(t: number): number
  elastic(bounciness?: number): EasingFunction
  exp(t: number): number
  in(easing: EasingFunction): EasingFunction
  inOut(easing: EasingFunction): EasingFunction
  linear(t: number): number
  out(easing: EasingFunction): EasingFunction
  poly(n: number): EasingFunction
  quad(t: number): number
  sin(t: number): number
  step0(n: number): number
  step1(n: number): number
}
declare const event: typeof $$AnimatedImplementation.event
declare const eventImpl: <T>(
  argMapping: ReadonlyArray<Mapping | null | undefined>,
  config: EventConfig<T>,
) => any
declare const findNodeHandle: <TElementType extends React.ElementType>(
  componentOrHandle:
    | (number | React.ComponentRef<TElementType>)
    | null
    | undefined,
  suppressWarning: boolean | null | undefined,
) => null | number | undefined
declare const flatten: typeof $$flattenStyle
declare const forkEvent: typeof $$AnimatedImplementation.forkEvent
declare const hairlineWidth: number
declare const I18nManager: {
  doLeftAndRightSwapInRTL: I18nManagerConstants["doLeftAndRightSwapInRTL"]
  isRTL: I18nManagerConstants["isRTL"]
  allowRTL: (shouldAllow: boolean) => void
  forceRTL: (shouldForce: boolean) => void
  getConstants: () => I18nManagerConstants
  swapLeftAndRightInRTL: (flipStyles: boolean) => void
}
declare const Image: ImageType
declare const InputAccessoryView: typeof InputAccessoryView_default
declare const InputAccessoryView_default: React.ComponentType<InputAccessoryViewProps>
declare const InteractionManager: typeof InteractionManagerStub_default
declare const InteractionManagerStub_default: {
  Events: {
    interactionComplete: "interactionComplete"
    interactionStart: "interactionStart"
  }
  addListener(
    eventType: string,
    listener: (...args: any) => unknown,
    context: unknown,
  ): EventSubscription
  clearInteractionHandle(handle: Handle): void
  createInteractionHandle(): Handle
  runAfterInteractions(task: null | Task | undefined): {
    cancel: () => void
    then: <U>(
      onFulfill?:
        | (($$PARAM_0$$: void) => (Promise<U> | U) | undefined)
        | undefined,
      onReject?: ((error: unknown) => (Promise<U> | U) | undefined) | undefined,
    ) => Promise<U>
  }
  setDeadline(deadline: number): void
}
declare const Keyboard: typeof Keyboard_default
declare const Keyboard_default: KeyboardImpl
declare const LayoutAnimation: typeof LayoutAnimation_default
declare const LayoutAnimation_default: {
  configureNext: typeof configureNext
  create: typeof createLayoutAnimation
  Presets: typeof Presets
  Properties: LayoutAnimationProperties
  setEnabled: typeof setLayoutAnimationEnabled
  Types: LayoutAnimationTypes
  easeInEaseOut: (onAnimationDidEnd?: OnAnimationDidEndCallback) => void
  linear: (onAnimationDidEnd?: OnAnimationDidEndCallback) => void
  spring: (onAnimationDidEnd?: OnAnimationDidEndCallback) => void
  checkConfig(...args: Array<unknown>): void
}
declare const Linking: typeof Linking_default
declare const Linking_default: LinkingImpl
declare const LogBox: ILogBox
declare const loop: typeof $$AnimatedImplementation.loop
declare const loopImpl: (
  animation: CompositeAnimation,
  $$PARAM_1$$?: LoopAnimationConfig,
) => CompositeAnimation
declare const Modal: typeof Wrapper_default
declare const modulo: typeof $$AnimatedImplementation.modulo
declare const moduloImpl: (
  a: AnimatedNode_default,
  modulus: number,
) => AnimatedModulo_default
declare const multiply: typeof $$AnimatedImplementation.multiply
declare const multiplyImpl: (
  a: AnimatedNode_default | number,
  b: AnimatedNode_default | number,
) => AnimatedMultiplication_default
declare const NativeAppEventEmitter: typeof RCTNativeAppEventEmitter_default
declare const NativeDeviceInfo_default: {
  getConstants(): DeviceInfoConstants
}
declare const NativeDialogManagerAndroid: typeof $$NativeDialogManagerAndroid
declare const NativeModules: typeof NativeModules_default
declare let NativeModules_default: {
  [moduleName: string]: any
}
declare const NativeText: HostComponent<NativeTextProps>
declare const NativeTouchable:
  | typeof TouchableNativeFeedback
  | typeof TouchableOpacity
declare const NativeVirtualText: HostComponent<NativeTextProps>
declare const Networking: typeof RCTNetworking_default
declare const PanResponder: typeof PanResponder_default
declare const PanResponder_default: {
  create(config: PanResponderCallbacks): {
    panHandlers: GestureResponderHandlerMethods
    getInteractionHandle: () => number | undefined
  }
}
declare const parallel: typeof $$AnimatedImplementation.parallel
declare const parallelImpl: (
  animations: Array<CompositeAnimation>,
  config?: null | ParallelConfig | undefined,
) => CompositeAnimation
declare const PermissionsAndroid: typeof PermissionsAndroidInstance_default
declare const PermissionsAndroidInstance_default: PermissionsAndroidImpl
declare const Platform: PlatformType
declare const Presets: {
  easeInEaseOut: LayoutAnimationConfig
  linear: LayoutAnimationConfig
  spring: LayoutAnimationConfig
}
declare const Pressable: (
  props: PressableProps & {
    ref?: React.Ref<React.ComponentRef<typeof View>>
  },
) => React.ReactNode
declare const processColor: typeof processColor_default
declare const ProgressBarAndroid: typeof ProgressBarAndroid_default
declare let ProgressBarAndroid_default: (
  props: Omit_2<
    Omit_2<ProgressBarAndroidProps, never>,
    keyof {
      ref?: React.Ref<React.ComponentRef<ProgressBarAndroidNativeComponentType>>
    }
  > & {
    ref?: React.Ref<React.ComponentRef<ProgressBarAndroidNativeComponentType>>
  },
) => React.ReactNode
declare const RCTNativeAppEventEmitter_default: typeof DeviceEventEmitter
declare const RCTNetworking_default: {
  abortRequest(requestId: number): void
  addListener<K extends keyof RCTNetworkingEventDefinitions>(
    eventType: K,
    listener: (...$$REST$$: RCTNetworkingEventDefinitions[K]) => unknown,
    context?: unknown,
  ): EventSubscription
  clearCookies(callback: (result: boolean) => void): void
  sendRequest(
    method: string,
    trackingName: string | void,
    url: string,
    headers: {},
    data: RequestBody,
    responseType: NativeResponseType,
    incrementalUpdates: boolean,
    timeout: number,
    callback: (requestId: number) => void,
    withCredentials: boolean,
  ): void
}
declare const registerCallableModule: typeof registerCallableModule_default
declare const registerCallableModule_default: RegisterCallableModule
declare const requireNativeComponent: typeof requireNativeComponent_default
declare const requireNativeComponent_default: <T extends {}>(
  uiViewClassName: string,
) => HostComponent<T>
declare const RootTagContext: React.Context<RootTag>
declare const SafeAreaView: typeof SafeAreaView_default
declare const SafeAreaView_default: (
  props: ViewProps & {
    ref?: React.Ref<React.ComponentRef<typeof View>>
  },
) => React.ReactNode
declare const ScrollView: typeof ScrollViewWrapper & ScrollViewComponentStatics
declare const ScrollViewContext_default: React.Context<Value>
declare const ScrollViewNativeComponent_default: HostComponent<ScrollViewNativeProps>
declare const ScrollViewWrapper: (
  props: ScrollViewProps & {
    ref?: React.Ref<PublicScrollViewInstance>
  },
) => React.ReactNode
declare const sequence: typeof $$AnimatedImplementation.sequence
declare const sequenceImpl: (
  animations: Array<CompositeAnimation>,
) => CompositeAnimation
declare const setStyleAttributePreprocessor: (
  property: string,
  process: (nextProp: any) => any,
) => void
declare const Settings: typeof Settings_default
declare let Settings_default: {
  clearWatch(watchId: number): void
  get(key: string): any
  set(settings: Object): void
  watchKeys(keys: Array<string> | string, callback: () => void): number
}
declare const spring: typeof $$AnimatedImplementation.spring
declare const springImpl: (
  value:
    | AnimatedColor_default
    | AnimatedValue_default
    | AnimatedValueXY_default,
  config: SpringAnimationConfig,
) => CompositeAnimation
declare const stagger: typeof $$AnimatedImplementation.stagger
declare const staggerImpl: (
  time: number,
  animations: Array<CompositeAnimation>,
) => CompositeAnimation
declare const States: {
  ERROR: "ERROR"
  NOT_RESPONDER: "NOT_RESPONDER"
  RESPONDER_ACTIVE_LONG_PRESS_IN: "RESPONDER_ACTIVE_LONG_PRESS_IN"
  RESPONDER_ACTIVE_LONG_PRESS_OUT: "RESPONDER_ACTIVE_LONG_PRESS_OUT"
  RESPONDER_ACTIVE_PRESS_IN: "RESPONDER_ACTIVE_PRESS_IN"
  RESPONDER_ACTIVE_PRESS_OUT: "RESPONDER_ACTIVE_PRESS_OUT"
  RESPONDER_INACTIVE_PRESS_IN: "RESPONDER_INACTIVE_PRESS_IN"
  RESPONDER_INACTIVE_PRESS_OUT: "RESPONDER_INACTIVE_PRESS_OUT"
}
declare const subtract: typeof $$AnimatedImplementation.subtract
declare const subtractImpl: (
  a: AnimatedNode_default | number,
  b: AnimatedNode_default | number,
) => AnimatedSubtraction_default
declare const Switch: typeof Switch_default
declare const Switch_default: (
  props: SwitchProps & {
    ref?: React.Ref<SwitchRef>
  },
) => React.ReactNode
declare const Text: typeof TextImpl_default
declare const TextImpl_default: (
  props: TextProps & {
    ref?: React.Ref<TextForwardRef>
  },
) => React.ReactNode
declare const TextInput: TextInputType
declare const timing: typeof $$AnimatedImplementation.timing
declare const timingImpl: (
  value:
    | AnimatedColor_default
    | AnimatedValue_default
    | AnimatedValueXY_default,
  config: TimingAnimationConfig,
) => CompositeAnimation
declare const ToastAndroid: typeof ToastAndroid_default
declare const ToastAndroid_default: {
  BOTTOM: number
  CENTER: number
  LONG: number
  SHORT: number
  TOP: number
  show: (message: string, duration: number) => void
  showWithGravity: (message: string, duration: number, gravity: number) => void
  showWithGravityAndOffset: (
    message: string,
    duration: number,
    gravity: number,
    xOffset: number,
    yOffset: number,
  ) => void
}
declare const Touchable: typeof TouchableImpl_default
declare const Touchable_default: (
  props: TouchableOpacityProps & {
    ref?: React.Ref<React.ComponentRef<typeof Animated.View>>
  },
) => React.ReactNode
declare const TouchableHighlight: typeof TouchableHighlight_default
declare const TouchableHighlight_default: (
  props: Readonly<Omit<TouchableHighlightProps, "hostRef">> & {
    ref?: React.Ref<React.ComponentRef<typeof View>>
  },
) => React.ReactNode
declare const TouchableImpl_default: {
  Mixin: typeof TouchableMixinImpl
  renderDebugView: ($$PARAM_0$$: {
    color: ColorValue
    hitSlop?: EdgeInsetsProp
  }) => null | React.ReactNode
}
declare const TouchableMixinImpl: {
  withoutDefaultFocusAndBlur: {}
  touchableGetInitialState: () => {
    touchable: {
      responderID: GestureResponderEvent["currentTarget"] | undefined
      touchState: TouchableState | undefined
    }
  }
  touchableHandleBlur: (e: BlurEvent) => void
  touchableHandleFocus: (e: FocusEvent) => void
  touchableHandleResponderGrant: (e: GestureResponderEvent) => void
  touchableHandleResponderMove: (e: GestureResponderEvent) => void
  touchableHandleResponderRelease: (e: GestureResponderEvent) => void
  touchableHandleResponderTerminate: (e: GestureResponderEvent) => void
  touchableHandleResponderTerminationRequest: () => any
  touchableHandleStartShouldSetResponder: () => any
  touchableLongPressCancelsPress: () => boolean
}
declare const TouchableOpacity: typeof Touchable_default
declare const TVEventControl: typeof TVEventControl_default
declare const TVEventControl_default: {
  disableGestureHandlersCancelTouches: () => void
  disableTVMenuKey: () => void
  disableTVPanGesture: () => void
  enableGestureHandlersCancelTouches: () => void
  enableTVMenuKey: () => void
  enableTVPanGesture: () => void
}
declare const TVEventHandler: typeof TVEventHandler_default
declare const TVEventHandler_default: TVEventHandlerType
declare const TVFocusGuideView: typeof TVFocusGuideView_default
declare const UIManager: typeof UIManager_default
declare const UIManager_default: UIManagerJSInterface
declare const unforkEvent: typeof $$AnimatedImplementation.unforkEvent
declare const useTVEventHandler: typeof useTVEventHandler_default
declare const useTVEventHandler_default: (
  handleEvent: (evt: TVRemoteEvent) => void,
) => void
declare const UTFSequence: typeof UTFSequence_default
declare const UTFSequence_default: {
  BOM: string
  BULLET: string
  BULLET_SP: string
  MDASH: string
  MDASH_SP: string
  MIDDOT: string
  MIDDOT_KATAKANA: string
  MIDDOT_SP: string
  NBSP: string
  NDASH: string
  NDASH_SP: string
  NEWLINE: string
  PIZZA: string
  TRIANGLE_LEFT: string
  TRIANGLE_RIGHT: string
}
declare const Vibration: typeof Vibration_default
declare const Vibration_default: {
  cancel: () => void
  vibrate: (pattern?: Array<number> | number, repeat?: boolean) => void
}
declare const View: typeof View_default
declare const ViewNativeComponent_default: HostComponent<ViewProps>
declare const VirtualizedList: typeof VirtualizedListComponent_default
declare const VirtualizedListComponent_default: VirtualizedListType
declare const VirtualizedListContext: React.Context<Context | null | undefined>
declare const VirtualizedSectionList: typeof VirtualizedSectionList_default
declare const VirtualizedSectionList_default: VirtualizedSectionListType
declare const VirtualizedSectionListComponent_default: <
  ItemT,
  SectionT extends SectionBase<
    ItemT,
    DefaultVirtualizedSectionT
  > = DefaultVirtualizedSectionT,
>(
  props: Omit<VirtualizedSectionListProps<ItemT, SectionT>, "ref"> & {
    ref?: React.Ref<{
      getListRef(): undefined | VirtualizedList_default
      scrollToLocation(params: ScrollToLocationParamsType): void
    }>
  },
) => React.ReactNode
declare type ____BlendMode_Internal =
  | "color-burn"
  | "color-dodge"
  | "color"
  | "darken"
  | "difference"
  | "exclusion"
  | "hard-light"
  | "hue"
  | "lighten"
  | "luminosity"
  | "multiply"
  | "normal"
  | "overlay"
  | "saturation"
  | "screen"
  | "soft-light"
declare type ____ColorValue_Internal = NativeColorValue | null | number | string
declare type ____DangerouslyImpreciseAnimatedStyleProp_Internal =
  WithAnimatedValue<StyleProp<Partial<____DangerouslyImpreciseStyle_Internal>>>
declare type ____DangerouslyImpreciseStyle_Internal = Readonly<
  ____DangerouslyImpreciseStyle_InternalCore &
    ____DangerouslyImpreciseStyle_InternalOverrides
>
declare type ____DangerouslyImpreciseStyle_InternalCore = Readonly<
  ____TextStyle_Internal & {
    objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down"
    overlayColor?: ColorValue
    resizeMode?: ImageResizeMode
    tintColor?: ____ColorValue_Internal
  }
>
declare type ____DangerouslyImpreciseStyle_InternalOverrides = {}
declare type ____FlattenStyleProp_Helper<
  TStyleProp extends StyleProp<unknown>,
  Depth extends FlattenDepthLimiter[keyof FlattenDepthLimiter] = 9,
> = Depth extends 0
  ? never
  : TStyleProp extends "" | false | null | void
    ? never
    : TStyleProp extends ReadonlyArray<infer V>
      ? ____FlattenStyleProp_Helper<
          V,
          Depth extends number ? FlattenDepthLimiter[Depth] : 0
        >
      : TStyleProp
declare type ____FlattenStyleProp_Internal<
  TStyleProp extends StyleProp<unknown>,
> =
  ____FlattenStyleProp_Helper<TStyleProp> extends never
    ? any
    : ____FlattenStyleProp_Helper<TStyleProp>
declare type ____FontVariant_Internal =
  | "common-ligatures"
  | "contextual"
  | "discretionary-ligatures"
  | "historical-ligatures"
  | "lining-nums"
  | "no-common-ligatures"
  | "no-contextual"
  | "no-discretionary-ligatures"
  | "no-historical-ligatures"
  | "oldstyle-nums"
  | "proportional-nums"
  | "small-caps"
  | "stylistic-eight"
  | "stylistic-eighteen"
  | "stylistic-eleven"
  | "stylistic-fifteen"
  | "stylistic-five"
  | "stylistic-four"
  | "stylistic-fourteen"
  | "stylistic-nine"
  | "stylistic-nineteen"
  | "stylistic-one"
  | "stylistic-seven"
  | "stylistic-seventeen"
  | "stylistic-six"
  | "stylistic-sixteen"
  | "stylistic-ten"
  | "stylistic-thirteen"
  | "stylistic-three"
  | "stylistic-twelve"
  | "stylistic-twenty"
  | "stylistic-two"
  | "tabular-nums"
declare type ____FontVariantArray_Internal =
  ReadonlyArray<____FontVariant_Internal>
declare type ____FontWeight_Internal =
  | "100"
  | "200"
  | "300"
  | "400"
  | "500"
  | "600"
  | "700"
  | "800"
  | "900"
  | "black"
  | "bold"
  | "condensed"
  | "condensedBold"
  | "heavy"
  | "light"
  | "medium"
  | "normal"
  | "regular"
  | "semibold"
  | "thin"
  | "ultralight"
  | 100
  | 200
  | 300
  | 400
  | 500
  | 600
  | 700
  | 800
  | 900
declare type ____ImageStyle_Internal = Readonly<
  ____ImageStyle_InternalCore & ____ImageStyle_InternalOverrides
>
declare type ____ImageStyle_InternalCore = Readonly<
  Omit<____ViewStyle_Internal, "overflow"> & {
    objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down"
    overflow?: "hidden" | "visible"
    overlayColor?: ColorValue
    resizeMode?: ImageResizeMode
    tintColor?: ____ColorValue_Internal
  }
>
declare type ____ImageStyle_InternalOverrides = {}
declare type ____ImageStyleProp_Internal = StyleProp<
  Readonly<Partial<____ImageStyle_Internal>>
>
declare type ____LayoutStyle_Internal = {
  readonly alignContent?:
    | "center"
    | "flex-end"
    | "flex-start"
    | "space-around"
    | "space-between"
    | "space-evenly"
    | "stretch"
  readonly alignItems?:
    | "baseline"
    | "center"
    | "flex-end"
    | "flex-start"
    | "stretch"
  readonly alignSelf?:
    | "auto"
    | "baseline"
    | "center"
    | "flex-end"
    | "flex-start"
    | "stretch"
  readonly aspectRatio?: number | string
  readonly borderBottomWidth?: number
  readonly borderEndWidth?: number
  readonly borderLeftWidth?: number
  readonly borderRightWidth?: number
  readonly borderStartWidth?: number
  readonly borderTopWidth?: number
  readonly borderWidth?: number
  readonly bottom?: DimensionValue
  readonly boxSizing?: "border-box" | "content-box"
  readonly columnGap?: number | string
  readonly direction?: "inherit" | "ltr" | "rtl"
  readonly display?: "contents" | "flex" | "none"
  readonly end?: DimensionValue
  readonly flex?: number
  readonly flexBasis?: number | string
  readonly flexDirection?: "column-reverse" | "column" | "row-reverse" | "row"
  readonly flexGrow?: number
  readonly flexShrink?: number
  readonly flexWrap?: "nowrap" | "wrap-reverse" | "wrap"
  readonly gap?: number | string
  readonly height?: DimensionValue
  readonly inset?: DimensionValue
  readonly insetBlock?: DimensionValue
  readonly insetBlockEnd?: DimensionValue
  readonly insetBlockStart?: DimensionValue
  readonly insetInline?: DimensionValue
  readonly insetInlineEnd?: DimensionValue
  readonly insetInlineStart?: DimensionValue
  readonly justifyContent?:
    | "center"
    | "flex-end"
    | "flex-start"
    | "space-around"
    | "space-between"
    | "space-evenly"
  readonly left?: DimensionValue
  readonly margin?: DimensionValue
  readonly marginBlock?: DimensionValue
  readonly marginBlockEnd?: DimensionValue
  readonly marginBlockStart?: DimensionValue
  readonly marginBottom?: DimensionValue
  readonly marginEnd?: DimensionValue
  readonly marginHorizontal?: DimensionValue
  readonly marginInline?: DimensionValue
  readonly marginInlineEnd?: DimensionValue
  readonly marginInlineStart?: DimensionValue
  readonly marginLeft?: DimensionValue
  readonly marginRight?: DimensionValue
  readonly marginStart?: DimensionValue
  readonly marginTop?: DimensionValue
  readonly marginVertical?: DimensionValue
  readonly maxHeight?: DimensionValue
  readonly maxWidth?: DimensionValue
  readonly minHeight?: DimensionValue
  readonly minWidth?: DimensionValue
  readonly overflow?: "hidden" | "scroll" | "visible"
  readonly padding?: DimensionValue
  readonly paddingBlock?: DimensionValue
  readonly paddingBlockEnd?: DimensionValue
  readonly paddingBlockStart?: DimensionValue
  readonly paddingBottom?: DimensionValue
  readonly paddingEnd?: DimensionValue
  readonly paddingHorizontal?: DimensionValue
  readonly paddingInline?: DimensionValue
  readonly paddingInlineEnd?: DimensionValue
  readonly paddingInlineStart?: DimensionValue
  readonly paddingLeft?: DimensionValue
  readonly paddingRight?: DimensionValue
  readonly paddingStart?: DimensionValue
  readonly paddingTop?: DimensionValue
  readonly paddingVertical?: DimensionValue
  readonly position?: "absolute" | "relative" | "static"
  readonly right?: DimensionValue
  readonly rowGap?: number | string
  readonly start?: DimensionValue
  readonly top?: DimensionValue
  readonly width?: DimensionValue
  readonly zIndex?: number
}
declare type ____ShadowStyle_Internal = Readonly<
  ____ShadowStyle_InternalCore & ____ShadowStyle_InternalOverrides
>
declare type ____ShadowStyle_InternalCore = {
  readonly shadowColor?: ____ColorValue_Internal
  readonly shadowOffset?: {
    readonly height?: number
    readonly width?: number
  }
  readonly shadowOpacity?: number
  readonly shadowRadius?: number
}
declare type ____ShadowStyle_InternalOverrides = {}
declare type ____Styles_Internal = {
  readonly [key: string]: Partial<____DangerouslyImpreciseStyle_Internal>
}
declare type ____TextStyle_Internal = Readonly<
  ____TextStyle_InternalCore & ____TextStyle_InternalOverrides
>
declare type ____TextStyle_InternalBase = {
  readonly color?: ____ColorValue_Internal
  readonly fontFamily?: string
  readonly fontSize?: number
  readonly fontStyle?: "italic" | "normal"
  readonly fontVariant?: ____FontVariantArray_Internal | string
  readonly fontWeight?: ____FontWeight_Internal
  readonly includeFontPadding?: boolean
  readonly letterSpacing?: number
  readonly lineHeight?: number
  readonly textAlign?: "auto" | "center" | "justify" | "left" | "right"
  readonly textAlignVertical?: "auto" | "bottom" | "center" | "top"
  readonly textDecorationColor?: ____ColorValue_Internal
  readonly textDecorationLine?:
    | "line-through"
    | "none"
    | "underline line-through"
    | "underline"
  readonly textDecorationStyle?: "dashed" | "dotted" | "double" | "solid"
  readonly textShadowColor?: ____ColorValue_Internal
  readonly textShadowOffset?: {
    readonly height: number
    readonly width: number
  }
  readonly textShadowRadius?: number
  readonly textTransform?: "capitalize" | "lowercase" | "none" | "uppercase"
  readonly userSelect?: "all" | "auto" | "contain" | "none" | "text"
  readonly verticalAlign?: "auto" | "bottom" | "middle" | "top"
  readonly writingDirection?: "auto" | "ltr" | "rtl"
}
declare type ____TextStyle_InternalCore = Readonly<
  ____ViewStyle_Internal & ____TextStyle_InternalBase
>
declare type ____TextStyle_InternalOverrides = {}
declare type ____TextStyleProp_Internal = StyleProp<
  Readonly<Partial<____TextStyle_Internal>>
>
declare type ____TransformStyle_Internal = {
  readonly transform?:
    | ReadonlyArray<
        Readonly<
          MaximumOneOf<
            MergeUnion<
              | {
                  readonly matrix:
                    | AnimatedNode_default
                    | ReadonlyArray<AnimatedNode_default | number>
                }
              | {
                  readonly perspective: AnimatedNode_default | number
                }
              | {
                  readonly rotate: AnimatedNode_default | string
                }
              | {
                  readonly rotateX: AnimatedNode_default | string
                }
              | {
                  readonly rotateY: AnimatedNode_default | string
                }
              | {
                  readonly rotateZ: AnimatedNode_default | string
                }
              | {
                  readonly scale: AnimatedNode_default | number
                }
              | {
                  readonly scaleX: AnimatedNode_default | number
                }
              | {
                  readonly scaleY: AnimatedNode_default | number
                }
              | {
                  readonly skewX: AnimatedNode_default | string
                }
              | {
                  readonly skewY: AnimatedNode_default | string
                }
              | {
                  readonly translate:
                    | [
                        AnimatedNode_default | number | string,
                        AnimatedNode_default | number | string,
                      ]
                    | AnimatedNode_default
                }
              | {
                  readonly translateX: AnimatedNode_default | number | string
                }
              | {
                  readonly translateY: AnimatedNode_default | number | string
                }
            >
          >
        >
      >
    | string
  readonly transformOrigin?:
    | [number | string, number | string, number | string]
    | string
}
declare type ____ViewStyle_Internal = Readonly<
  ____ViewStyle_InternalCore & ____ViewStyle_InternalOverrides
>
declare type ____ViewStyle_InternalBase = {
  readonly backfaceVisibility?: "hidden" | "visible"
  readonly backgroundColor?: ____ColorValue_Internal
  readonly borderBlockColor?: ____ColorValue_Internal
  readonly borderBlockEndColor?: ____ColorValue_Internal
  readonly borderBlockStartColor?: ____ColorValue_Internal
  readonly borderBottomColor?: ____ColorValue_Internal
  readonly borderBottomEndRadius?: number | string
  readonly borderBottomLeftRadius?: number | string
  readonly borderBottomRightRadius?: number | string
  readonly borderBottomStartRadius?: number | string
  readonly borderBottomWidth?: number
  readonly borderColor?: ____ColorValue_Internal
  readonly borderCurve?: "circular" | "continuous"
  readonly borderEndColor?: ____ColorValue_Internal
  readonly borderEndEndRadius?: number | string
  readonly borderEndStartRadius?: number | string
  readonly borderEndWidth?: number
  readonly borderLeftColor?: ____ColorValue_Internal
  readonly borderLeftWidth?: number
  readonly borderRadius?: number | string
  readonly borderRightColor?: ____ColorValue_Internal
  readonly borderRightWidth?: number
  readonly borderStartColor?: ____ColorValue_Internal
  readonly borderStartEndRadius?: number | string
  readonly borderStartStartRadius?: number | string
  readonly borderStartWidth?: number
  readonly borderStyle?: "dashed" | "dotted" | "solid"
  readonly borderTopColor?: ____ColorValue_Internal
  readonly borderTopEndRadius?: number | string
  readonly borderTopLeftRadius?: number | string
  readonly borderTopRightRadius?: number | string
  readonly borderTopStartRadius?: number | string
  readonly borderTopWidth?: number
  readonly borderWidth?: number
  readonly boxShadow?: ReadonlyArray<BoxShadowValue> | string
  readonly cursor?: CursorValue
  readonly elevation?: number
  readonly filter?: ReadonlyArray<FilterFunction> | string
  readonly isolation?: "auto" | "isolate"
  readonly mixBlendMode?: ____BlendMode_Internal
  readonly opacity?: number
  readonly outlineColor?: ____ColorValue_Internal
  readonly outlineOffset?: number
  readonly outlineStyle?: "dashed" | "dotted" | "solid"
  readonly outlineWidth?: number
  readonly pointerEvents?: "auto" | "box-none" | "box-only" | "none"
}
declare type ____ViewStyle_InternalCore = Readonly<
  ____LayoutStyle_Internal &
    ____ShadowStyle_Internal &
    ____TransformStyle_Internal &
    ____ViewStyle_InternalBase
>
declare type ____ViewStyle_InternalOverrides = {}
declare type ____ViewStyleProp_Internal = StyleProp<
  Readonly<Partial<____ViewStyle_Internal>>
>
declare class _TextInputInstance extends ReactNativeElement_default {
  clear(): void
  getNativeRef(): ReactNativeElement_default | undefined
  isFocused(): boolean
  setSelection(start: number, end: number): void
}
declare type $$AndroidSwitchNativeComponent =
  typeof $$AndroidSwitchNativeComponent
declare type $$AnimatedFlatList = typeof $$AnimatedFlatList
declare type $$AnimatedImage = typeof $$AnimatedImage
declare type $$AnimatedImplementation = typeof $$AnimatedImplementation
declare type $$AnimatedScrollView = typeof $$AnimatedScrollView
declare type $$AnimatedSectionList = typeof $$AnimatedSectionList
declare type $$AnimatedText = typeof $$AnimatedText
declare type $$AnimatedView = typeof $$AnimatedView
declare type $$flattenStyle = typeof $$flattenStyle
declare type $$index = typeof $$index
declare type $$NativeDeviceInfo = typeof $$NativeDeviceInfo
declare type $$NativeDialogManagerAndroid = typeof $$NativeDialogManagerAndroid
declare type $$ProgressBarAndroidNativeComponent =
  typeof $$ProgressBarAndroidNativeComponent
declare type $$ScrollViewContext = typeof $$ScrollViewContext
declare type $$ScrollViewNativeComponent = typeof $$ScrollViewNativeComponent
declare type $$SwitchNativeComponent = typeof $$SwitchNativeComponent
declare type $$ViewNativeComponent = typeof $$ViewNativeComponent
declare type absoluteFill = typeof absoluteFill
declare type absoluteFillObject = typeof absoluteFillObject
declare type AbsoluteFillStyle = {
  readonly bottom: 0
  readonly left: 0
  readonly position: "absolute"
  readonly right: 0
  readonly top: 0
}
declare type AbstractImageAndroid = (
  props: ImageProps & {
    ref?: React.Ref<HostInstance>
  },
) => React.ReactNode
declare type AbstractImageIOS = (
  props: ImageProps & {
    ref?: React.Ref<HostInstance>
  },
) => React.ReactNode
declare type AccessibilityActionEvent = NativeSyntheticEvent<{
  readonly actionName: string
}>
declare type AccessibilityActionInfo = {
  readonly label?: string
  readonly name: AccessibilityActionName | string
}
declare type AccessibilityActionName =
  | "activate"
  | "decrement"
  | "escape"
  | "increment"
  | "longpress"
  | "magicTap"
declare type AccessibilityEventDefinitions =
  AccessibilityEventDefinitionsAndroid &
    AccessibilityEventDefinitionsIOS & {
      change: [boolean]
      reduceMotionChanged: [boolean]
      screenReaderChanged: [boolean]
    }
declare type AccessibilityEventDefinitionsAndroid = {
  accessibilityServiceChanged: [boolean]
  highTextContrastChanged: [boolean]
}
declare type AccessibilityEventDefinitionsIOS = {
  announcementFinished: [
    {
      announcement: string
      success: boolean
    },
  ]
  boldTextChanged: [boolean]
  darkerSystemColorsChanged: [boolean]
  grayscaleChanged: [boolean]
  invertColorsChanged: [boolean]
  reduceTransparencyChanged: [boolean]
}
declare type AccessibilityEventTypes = "click" | "focus" | "viewHoverEnter"
declare type AccessibilityInfo = typeof AccessibilityInfo
declare type AccessibilityProps = Readonly<
  AccessibilityPropsAndroid &
    AccessibilityPropsIOS & {
      accessibilityActions?: ReadonlyArray<AccessibilityActionInfo>
      accessibilityHint?: string
      accessibilityLabel?: string
      accessibilityRole?: AccessibilityRole
      accessibilityState?: AccessibilityState
      accessibilityValue?: AccessibilityValue
      accessible?: boolean
      "aria-busy"?: boolean
      "aria-checked"?: "mixed" | (boolean | undefined)
      "aria-disabled"?: boolean
      "aria-expanded"?: boolean
      "aria-hidden"?: boolean
      "aria-label"?: string
      "aria-selected"?: boolean
      "aria-valuemax"?: AccessibilityValue["max"]
      "aria-valuemin"?: AccessibilityValue["min"]
      "aria-valuenow"?: AccessibilityValue["now"]
      "aria-valuetext"?: AccessibilityValue["text"]
      role?: Role
    }
>
declare type AccessibilityPropsAndroid = {
  readonly accessibilityLabelledBy?:
    | (Array<string> | undefined)
    | (string | undefined)
  readonly accessibilityLiveRegion?: "assertive" | "none" | "polite"
  readonly "aria-labelledby"?: string
  readonly "aria-live"?: "assertive" | "off" | "polite"
  readonly importantForAccessibility?:
    | "auto"
    | "no-hide-descendants"
    | "no"
    | "yes"
  readonly screenReaderFocusable?: boolean
}
declare type AccessibilityPropsIOS = {
  readonly accessibilityElementsHidden?: boolean
  readonly accessibilityIgnoresInvertColors?: boolean
  readonly accessibilityLanguage?: string
  readonly accessibilityLargeContentTitle?: string
  readonly accessibilityRespondsToUserInteraction?: boolean
  readonly accessibilityShowsLargeContentViewer?: boolean
  readonly accessibilityViewIsModal?: boolean
  readonly "aria-modal"?: boolean
}
declare type AccessibilityRole =
  | "adjustable"
  | "alert"
  | "button"
  | "checkbox"
  | "combobox"
  | "drawerlayout"
  | "dropdownlist"
  | "grid"
  | "header"
  | "horizontalscrollview"
  | "iconmenu"
  | "image"
  | "imagebutton"
  | "keyboardkey"
  | "link"
  | "list"
  | "menu"
  | "menubar"
  | "menuitem"
  | "none"
  | "pager"
  | "progressbar"
  | "radio"
  | "radiogroup"
  | "scrollbar"
  | "scrollview"
  | "search"
  | "slidingdrawer"
  | "spinbutton"
  | "summary"
  | "switch"
  | "tab"
  | "tabbar"
  | "tablist"
  | "text"
  | "timer"
  | "togglebutton"
  | "toolbar"
  | "viewgroup"
  | "webview"
  | string
declare type AccessibilityState = {
  busy?: boolean
  checked?: "mixed" | (boolean | undefined)
  disabled?: boolean
  expanded?: boolean
  selected?: boolean
}
declare type AccessibilityValue = {
  readonly max?: number
  readonly min?: number
  readonly now?: number
  readonly text?: string
}
declare type ActionSheetIOS = typeof ActionSheetIOS
declare type ActionSheetIOSOptions = {
  readonly anchor?: number
  readonly cancelButtonIndex?: number
  readonly cancelButtonTintColor?: ColorValue | ProcessedColorValue
  readonly destructiveButtonIndex?:
    | (Array<number> | undefined)
    | (number | undefined)
  readonly disabledButtonIndices?: Array<number>
  readonly disabledButtonTintColor?: ColorValue | ProcessedColorValue
  readonly message?: string
  readonly options: Array<string>
  readonly tintColor?: ColorValue | ProcessedColorValue
  readonly title?: string
  readonly userInterfaceStyle?: string
}
declare type ActiveCallback = (
  event: GestureResponderEvent,
  gestureState: PanResponderGestureState,
) => boolean
declare type ActivityIndicator = typeof ActivityIndicator
declare type ActivityIndicatorIOSProps = {
  readonly hidesWhenStopped?: boolean
}
declare type ActivityIndicatorProps = Readonly<
  ViewProps &
    ActivityIndicatorIOSProps & {
      animating?: boolean
      color?: ColorValue
      size?: IndicatorSize
    }
>
declare type add = typeof add
declare function addChangeListener(
  listener: ($$PARAM_0$$: { colorScheme: ColorSchemeName | undefined }) => void,
): EventSubscription
declare class Alert {
  static alert(
    title: null | string | undefined,
    message?: null | string | undefined,
    buttons?: AlertButtons,
    options?: AlertOptions,
  ): void
  static prompt(
    title: null | string | undefined,
    message?: null | string | undefined,
    callbackOrButtons?:
      | (((text: string) => void) | AlertButtons)
      | null
      | undefined,
    type?: AlertType | null | undefined,
    defaultValue?: string,
    keyboardType?: string,
    options?: AlertOptions,
  ): void
}
declare type AlertButton = {
  isPreferred?: boolean
  onPress?: (((value?: string) => any) | undefined) | (Function | undefined)
  style?: AlertButtonStyle
  text?: string
}
declare type AlertButtons = Array<AlertButton>
declare type AlertButtonStyle = "cancel" | "default" | "destructive"
declare type AlertOptions = {
  cancelable?: boolean
  onDismiss?: () => void
  userInterfaceStyle?: "dark" | "light" | "unspecified"
}
declare type AlertType =
  | "default"
  | "login-password"
  | "plain-text"
  | "secure-text"
declare type AndroidDrawable = AndroidDrawableRipple | AndroidDrawableThemeAttr
declare type AndroidDrawableRipple = {
  readonly borderless?: boolean
  readonly color?: number
  readonly rippleRadius?: number
  readonly type: "RippleAndroid"
}
declare type AndroidDrawableThemeAttr = {
  readonly attribute: string
  readonly type: "ThemeAttrAndroid"
}
declare type AndroidKeyboardEvent = Readonly<
  Omit<BaseKeyboardEvent, "duration" | "easing"> & {
    duration: 0
    easing: "keyboard"
  }
>
declare type AndroidPlatform = {
  OS: "android"
  select: <T>(spec: PlatformSelectSpec<T>) => T
  get constants(): {
    Brand: string
    Fingerprint: string
    isDisableAnimations?: boolean
    isTesting: boolean
    Manufacturer: string
    Model: string
    reactNativeVersion: {
      major: number
      minor: number
      patch: number
      prerelease: string | undefined
    }
    Release: string
    Serial: string
    ServerHost?: string
    uiMode: string
    Version: number
  }
  get isDisableAnimations(): boolean
  get isTesting(): boolean
  get isTV(): boolean
  get isVision(): boolean
  get Version(): number
}
declare type AndroidProgressBarNativeProps = Readonly<
  Omit<ViewProps, "testID"> & {
    animating?: WithDefault<boolean, true>
    color?: ColorValue
    indeterminate: boolean
    progress?: WithDefault<Double, 0>
    styleAttr?: string
    testID?: WithDefault<string, "">
    typeAttr?: string
  }
>
declare type AndroidSwitchChangeEvent = {
  readonly target: Int32
  readonly value: boolean
}
declare type AndroidSwitchNativeProps = Readonly<
  Omit<ViewProps, "enabled"> & {
    disabled?: WithDefault<boolean, false>
    enabled?: WithDefault<boolean, true>
    on?: WithDefault<boolean, false>
    onChange?: BubblingEventHandler<AndroidSwitchChangeEvent>
    thumbColor?: ColorValue
    thumbTintColor?: ColorValue
    trackColorForFalse?: ColorValue
    trackColorForTrue?: ColorValue
    trackTintColor?: ColorValue
    value?: WithDefault<boolean, false>
  }
>
declare namespace Animated {
  export {
    CompositeAnimation,
    DecayAnimationConfig,
    SpringAnimationConfig,
    TimingAnimationConfig,
    $$AnimatedFlatList as FlatList,
    $$AnimatedImage as Image,
    $$AnimatedScrollView as ScrollView,
    $$AnimatedSectionList as SectionList,
    $$AnimatedText as Text,
    $$AnimatedView as View,
    AnimatedColor_default as Color,
    AnimatedEvent as Event,
    AnimatedInterpolation_default as Interpolation,
    AnimatedNode_default as Node,
    AnimatedValue_default as Value,
    AnimatedValueXY_default as ValueXY,
    AnimatedInterpolation_default as AnimatedInterpolation,
    AnimatedColor_default as AnimatedColor,
    AnimatedValueConfig as AnimatedConfig,
    AnimatedNode_default as AnimatedNode,
    AnimatedAddition_default as AnimatedAddition,
    AnimatedDiffClamp_default as AnimatedDiffClamp,
    AnimatedDivision_default as AnimatedDivision,
    InterpolationConfigType as InterpolationConfig,
    AnimatedModulo_default as AnimatedModulo,
    AnimatedMultiplication_default as AnimatedMultiplication,
    AnimatedSubtraction_default as AnimatedSubtraction,
    WithAnimatedValue,
    AnimatedProps,
    AnimatedComponentType as AnimatedComponent,
    add,
    attachNativeEvent,
    createAnimatedComponent,
    decay,
    delay,
    diffClamp,
    divide,
    event,
    forkEvent,
    loop,
    modulo,
    multiply,
    parallel,
    sequence,
    spring,
    stagger,
    subtract,
    timing,
    unforkEvent,
  }
}
declare class AnimatedAddition_default extends AnimatedWithChildren_default {
  constructor(
    a: AnimatedNode_default | number,
    b: AnimatedNode_default | number,
    config?: AnimatedNodeConfig | null | undefined,
  )
  interpolate<OutputT extends number | string>(
    config: InterpolationConfigType<OutputT>,
  ): AnimatedInterpolation_default<OutputT>
}
declare class AnimatedColor_default extends AnimatedWithChildren_default {
  a: AnimatedValue_default
  b: AnimatedValue_default
  g: AnimatedValue_default
  nativeColor: NativeColorValue | null | undefined
  r: AnimatedValue_default
  constructor(
    valueIn?: InputValue,
    config?: AnimatedColorConfig | null | undefined,
  )
  extractOffset(): void
  flattenOffset(): void
  resetAnimation(callback?: ColorListenerCallback): void
  setOffset(offset: RgbaValue): void
  setValue(value: ColorValue | RgbaValue): void
  stopAnimation(callback?: ColorListenerCallback): void
}
declare type AnimatedColorConfig = Readonly<
  AnimatedNodeConfig & {
    useNativeDriver: boolean
  }
>
declare type AnimatedComponentType<Props extends {}, Instance = unknown> = (
  props: Omit<AnimatedProps<Props>, "ref"> & {
    ref?: React.Ref<Instance>
  },
) => React.ReactNode
declare class AnimatedDiffClamp_default extends AnimatedWithChildren_default {
  constructor(
    a: AnimatedNode_default,
    min: number,
    max: number,
    config?: AnimatedNodeConfig | null | undefined,
  )
  interpolate<OutputT extends number | string>(
    config: InterpolationConfigType<OutputT>,
  ): AnimatedInterpolation_default<OutputT>
}
declare class AnimatedDivision_default extends AnimatedWithChildren_default {
  constructor(
    a: AnimatedNode_default | number,
    b: AnimatedNode_default | number,
    config?: AnimatedNodeConfig | null | undefined,
  )
  interpolate<OutputT extends number | string>(
    config: InterpolationConfigType<OutputT>,
  ): AnimatedInterpolation_default<OutputT>
}
declare class AnimatedEvent {
  constructor(
    argMapping: ReadonlyArray<Mapping | null | undefined>,
    config: EventConfig<any>,
  )
}
declare class AnimatedInterpolation_default<
  OutputT extends number | string,
> extends AnimatedWithChildren_default {
  constructor(
    parent: AnimatedNode_default,
    config: InterpolationConfigType<OutputT>,
  )
  interpolate<NewOutputT extends number | string>(
    config: InterpolationConfigType<NewOutputT>,
  ): AnimatedInterpolation_default<NewOutputT>
}
declare class AnimatedModulo_default extends AnimatedWithChildren_default {
  constructor(
    a: AnimatedNode_default,
    modulus: number,
    config?: AnimatedNodeConfig | null | undefined,
  )
  interpolate<OutputT extends number | string>(
    config: InterpolationConfigType<OutputT>,
  ): AnimatedInterpolation_default<OutputT>
}
declare class AnimatedMultiplication_default extends AnimatedWithChildren_default {
  constructor(
    a: AnimatedNode_default | number,
    b: AnimatedNode_default | number,
    config?: AnimatedNodeConfig | null | undefined,
  )
  interpolate<OutputT extends number | string>(
    config: InterpolationConfigType<OutputT>,
  ): AnimatedInterpolation_default<OutputT>
}
declare class AnimatedNode_default {
  addListener(callback: (value: any) => unknown): string
  constructor(config?: null | Readonly<AnimatedNodeConfig> | undefined)
  hasListeners(): boolean
  removeAllListeners(): void
  removeListener(id: string): void
  toJSON(): unknown
}
declare type AnimatedNodeConfig = {
  readonly debugID?: string
}
declare type AnimatedProps<Props extends {}> = LooseOmit<
  {
    [K in keyof Props]: K extends NonAnimatedProps
      ? Props[K]
      : WithAnimatedValue<Props[K]>
  },
  "ref"
> &
  PassThroughProps
declare type AnimatedScrollViewInstance = React.ComponentRef<typeof ScrollView>
declare class AnimatedSubtraction_default extends AnimatedWithChildren_default {
  constructor(
    a: AnimatedNode_default | number,
    b: AnimatedNode_default | number,
    config?: AnimatedNodeConfig | null | undefined,
  )
  interpolate<OutputT extends number | string>(
    config: InterpolationConfigType<OutputT>,
  ): AnimatedInterpolation_default<OutputT>
}
declare class AnimatedTracking_default extends AnimatedNode_default {
  constructor(
    value: AnimatedValue_default,
    parent: AnimatedNode_default,
    animationClass: any,
    animationConfig: Object,
    callback?: EndCallback | null | undefined,
    config?: AnimatedNodeConfig | null | undefined,
  )
  update(): void
}
declare class AnimatedValue_default extends AnimatedWithChildren_default {
  addListener(callback: (value: any) => unknown): string
  animate(
    animation: Animation_default,
    callback: EndCallback | null | undefined,
  ): void
  constructor(value: number, config?: AnimatedValueConfig | null | undefined)
  extractOffset(): void
  flattenOffset(): void
  interpolate<OutputT extends number | string>(
    config: InterpolationConfigType<OutputT>,
  ): AnimatedInterpolation_default<OutputT>
  removeAllListeners(): void
  removeListener(id: string): void
  resetAnimation(callback?: ((value: number) => void) | null | undefined): void
  setOffset(offset: number): void
  setValue(value: number): void
  stopAnimation(callback?: ((value: number) => void) | null | undefined): void
  stopTracking(): void
  track(tracking: AnimatedTracking_default): void
}
declare type AnimatedValueConfig = Readonly<
  AnimatedNodeConfig & {
    useNativeDriver: boolean
  }
>
declare class AnimatedValueXY_default extends AnimatedWithChildren_default {
  x: AnimatedValue_default
  y: AnimatedValue_default
  addListener(callback: ValueXYListenerCallback): string
  constructor(
    valueIn?:
      | null
      | undefined
      | {
          readonly x: AnimatedValue_default | number
          readonly y: AnimatedValue_default | number
        },
    config?: AnimatedValueXYConfig | null | undefined,
  )
  extractOffset(): void
  flattenOffset(): void
  getLayout(): {
    [key: string]: AnimatedValue_default
  }
  getTranslateTransform(): Array<
    | {
        translateX: AnimatedValue_default
      }
    | {
        translateY: AnimatedValue_default
      }
  >
  removeAllListeners(): void
  removeListener(id: string): void
  resetAnimation(callback?: (value: { x: number; y: number }) => void): void
  setOffset(offset: { x: number; y: number }): void
  setValue(value: { x: number; y: number }): void
  stopAnimation(callback?: (value: { x: number; y: number }) => void): void
}
declare type AnimatedValueXYConfig = Readonly<
  AnimatedNodeConfig & {
    useNativeDriver: boolean
  }
>
declare class AnimatedWithChildren_default extends AnimatedNode_default {}
declare class Animation_default {
  constructor(config: AnimationConfig)
  start(
    fromValue: number,
    onUpdate: (value: number) => void,
    onEnd: EndCallback | null | undefined,
    previousAnimation: Animation_default | null | undefined,
    animatedValue: AnimatedValue_default,
  ): void
  stop(): void
}
declare type AnimationConfig = {
  readonly debugID?: string
  readonly isInteraction?: boolean
  readonly isLooping?: boolean
  readonly iterations?: number
  readonly onComplete?: EndCallback
  readonly platformConfig?: PlatformConfig
  readonly useNativeDriver: boolean
}
declare type AnyAttributeType = AttributeType<any, any>
declare type AnyVirtualizedSectionList =
  typeof VirtualizedSectionListComponent_default
declare type AppConfig = {
  appKey: string
  component?: ComponentProvider
  run?: Runnable
  section?: boolean
}
declare namespace Appearance {
  export {
    getColorScheme,
    setColorScheme,
    addChangeListener,
    AppearancePreferences,
  }
}
declare type AppearancePreferences = {
  colorScheme?: ColorSchemeName
}
declare type AppParameters = {
  fabric?: boolean
  initialProps: {
    readonly [$$Key$$: string]: unknown
  }
  rootTag: RootTag
}
declare namespace AppRegistry {
  export {
    setWrapperComponentProvider,
    setRootViewStyleProvider,
    registerConfig,
    registerComponent,
    registerRunnable,
    registerSection,
    getAppKeys,
    getSectionKeys,
    getSections,
    getRunnable,
    getRegistry,
    setComponentProviderInstrumentationHook,
    runApplication,
    setSurfaceProps,
    unmountApplicationComponentAtRootTag,
    registerHeadlessTask,
    registerCancellableHeadlessTask,
    startHeadlessTask,
    cancelHeadlessTask,
    TaskCanceller,
    TaskCancelProvider,
  }
}
declare type AppState = typeof AppState
declare type AppStateEvent = keyof AppStateEventDefinitions
declare type AppStateEventDefinitions = {
  blur: []
  change: [AppStateStatus]
  focus: []
  memoryWarning: []
}
declare class AppStateImpl {
  currentState: null | string | undefined
  isAvailable: boolean
  addEventListener<K extends AppStateEvent>(
    type: K,
    handler: (...$$REST$$: AppStateEventDefinitions[K]) => void,
  ): EventSubscription
  constructor()
}
declare type AppStateStatus =
  | "active"
  | "background"
  | "extension"
  | "inactive"
  | "unknown"
declare interface ArrayLike_2<T> extends Iterable<T> {
  [indexer: number]: T
  readonly length: number
}
declare type attachNativeEvent = typeof attachNativeEvent
declare function attachNativeEventImpl(
  viewRef: any,
  eventName: string,
  argMapping: ReadonlyArray<Mapping | null | undefined>,
  platformConfig: null | PlatformConfig | undefined,
): {
  detach: () => void
}
declare type AttributeConfiguration = {
  readonly [propName: string]: AnyAttributeType | void
  readonly style?: {
    readonly [propName: string]: AnyAttributeType
  }
}
declare type AttributeType<T, V> =
  | true
  | {
      readonly diff?: (arg1: T, arg2: T) => boolean
      readonly process?: (arg1: V) => T
    }
declare type AutoCapitalize = "characters" | "none" | "sentences" | "words"
declare type BackHandler = typeof BackHandler
declare type BackPressEventName = "backPress" | "hardwareBackPress"
declare type BaseKeyboardEvent = {
  duration: number
  easing: KeyboardEventEasing
  endCoordinates: KeyboardMetrics
}
declare function beginAsyncEvent(eventName: EventName, args?: EventArgs): number
declare function beginEvent(eventName: EventName, args?: EventArgs): void
declare class Blob_default {
  close(): void
  constructor(parts?: Array<Blob_default | string>, options?: BlobOptions)
  set data(data: BlobData | null | undefined)
  get data(): BlobData
  get size(): number
  slice(start?: number, end?: number, contentType?: string): Blob_default
  get type(): string
}
declare type BlobData = {
  blobId: string
  lastModified?: number
  name?: string
  offset: number
  size: number
  type?: string
}
declare type BlobOptions = {
  lastModified: number
  type: string
}
declare type BlurEvent = NativeSyntheticEvent<TargetedEvent>
declare type BoxShadowValue = {
  blurRadius?: number | string
  color?: ____ColorValue_Internal
  inset?: boolean
  offsetX: number | string
  offsetY: number | string
  spreadDistance?: number | string
}
declare type BubblingEventHandler<
  T,
  PaperName extends never | string = never,
> = (event: NativeSyntheticEvent<T>) => Promise<void> | void
declare type Builtin = (
  ...$$REST$$: ReadonlyArray<never>
) => Date | Error | RegExp | unknown
declare type Button = typeof Button
declare type ButtonProps = {
  readonly accessibilityActions?: ReadonlyArray<AccessibilityActionInfo>
  readonly accessibilityHint?: string
  readonly accessibilityLabel?: string
  readonly accessibilityLanguage?: string
  readonly accessibilityState?: AccessibilityState
  readonly accessible?: boolean
  readonly "aria-busy"?: boolean
  readonly "aria-checked"?: "mixed" | (boolean | undefined)
  readonly "aria-disabled"?: boolean
  readonly "aria-expanded"?: boolean
  readonly "aria-label"?: string
  readonly "aria-selected"?: boolean
  readonly color?: ColorValue
  readonly disabled?: boolean
  readonly hasTVPreferredFocus?: boolean
  readonly importantForAccessibility?:
    | "auto"
    | "no-hide-descendants"
    | "no"
    | "yes"
  readonly nextFocusDown?: number
  readonly nextFocusForward?: number
  readonly nextFocusLeft?: number
  readonly nextFocusRight?: number
  readonly nextFocusUp?: number
  readonly onAccessibilityAction?: (event: AccessibilityActionEvent) => unknown
  readonly testID?: string
  readonly title: string
  readonly touchSoundDisabled?: boolean
  readonly onPress?: (event?: GestureResponderEvent) => unknown
}
declare type ButtonRef = React.ComponentRef<typeof NativeTouchable>
declare function cancelHeadlessTask(taskId: number, taskKey: string): void
declare type Category = string
declare type CellMetricProps = {
  data: VirtualizedListProps["data"]
  getItem: VirtualizedListProps["getItem"]
  getItemCount: VirtualizedListProps["getItemCount"]
  getItemLayout?: VirtualizedListProps["getItemLayout"]
  keyExtractor?: VirtualizedListProps["keyExtractor"]
}
declare type CellMetrics = {
  index: number
  isMounted: boolean
  length: number
  offset: number
}
declare type CellRegion = {
  first: number
  isSpacer: boolean
  last: number
}
declare type CellRendererProps<ItemT> = {
  readonly cellKey: string
  readonly children: React.ReactNode
  readonly index: number
  readonly item: ItemT
  readonly style: StyleProp<ViewStyle>
  readonly onFocusCapture?: (event: FocusEvent) => void
  readonly onLayout?: (event: LayoutChangeEvent) => void
}
declare class CellRenderMask {
  addCells(cells: { first: number; last: number }): void
  constructor(numCells: number)
  enumerateRegions(): ReadonlyArray<CellRegion>
  equals(other: CellRenderMask): boolean
  numCells(): number
}
declare type Clipboard = typeof Clipboard
declare type CodeFrame = {
  readonly collapse?: boolean
  readonly content: string
  readonly fileName: string
  readonly location:
    | undefined
    | {
        column: number
        row: number
      }
}
declare type codegenNativeCommands = typeof codegenNativeCommands
declare function codegenNativeCommands_default<T extends {}>(
  options: NativeCommandsOptions<keyof T>,
): T
declare type codegenNativeComponent = typeof codegenNativeComponent
declare function codegenNativeComponent_default<Props extends {}>(
  componentName: string,
  options?: NativeComponentOptions,
): NativeComponentType<Props>
declare namespace CodegenTypes {
  export {
    BubblingEventHandler,
    DirectEventHandler,
    Double,
    Float,
    Int32,
    UnsafeObject,
    UnsafeMixed,
    DefaultTypes,
    WithDefault,
    EventEmitter,
  }
}
declare type ColorListenerCallback = (value: ColorValue) => unknown
declare type ColorSchemeName = "dark" | "light" | "unspecified"
declare type ColorValue = ____ColorValue_Internal
declare type ComponentOrHandleType =
  | (number | React.ComponentRef<React.ElementType>)
  | null
  | undefined
declare type ComponentProvider = () => React.ComponentType<any>
declare type ComponentProviderInstrumentationHook = (
  component_: ComponentProvider,
  scopedPerformanceLogger: IPerformanceLogger,
) => React.ComponentType<any>
declare type ComponentStack = ReadonlyArray<CodeFrame>
declare type ComponentStackType = "legacy" | "stack"
declare type ComponentType = HostComponent<SwitchNativeProps>
declare type compose = typeof compose
declare function composeStyles_default<T, U extends T, V extends T>(
  style1: null | U | undefined,
  style2: null | undefined | V,
): (ReadonlyArray<T> | T) | null | undefined
declare type CompositeAnimation = {
  reset: () => void
  start: (callback?: EndCallback | undefined, isLooping?: boolean) => void
  stop: () => void
}
declare function configureNext(
  config: LayoutAnimationConfig,
  onAnimationDidEnd?: OnAnimationDidEndCallback,
  onAnimationDidFail?: OnAnimationDidFailCallback,
): void
declare type ContentAvailable = 1 | null | void
declare type Context = {
  readonly cellKey: string | undefined
  readonly horizontal: boolean | undefined
  readonly getOutermostParentListRef: () => VirtualizedList_default
  readonly getScrollMetrics: () => {
    contentLength: number
    dOffset: number
    dt: number
    offset: number
    timestamp: number
    velocity: number
    visibleLength: number
    zoomScale: number
  }
  readonly registerAsNestedChild: ($$PARAM_0$$: {
    cellKey: string
    ref: VirtualizedList_default
  }) => void
  readonly unregisterAsNestedChild: ($$PARAM_0$$: {
    ref: VirtualizedList_default
  }) => void
}
declare function counterEvent(eventName: EventName, value: number): void
declare type create = typeof create
declare type createAnimatedComponent = typeof createAnimatedComponent
declare function createAnimatedComponent_default<
  TInstance extends React.ComponentType<any>,
>(
  Component: TInstance,
): AnimatedComponentType<
  Readonly<
    React.JSX.LibraryManagedAttributes<
      TInstance,
      React.ComponentProps<TInstance>
    >
  >,
  React.ComponentRef<TInstance>
>
declare function createLayoutAnimation(
  duration: number,
  type?: LayoutAnimationType,
  property?: LayoutAnimationProperty,
): LayoutAnimationConfig
declare function createPublicTextInstance(
  internalInstanceHandle: InternalInstanceHandle,
  ownerDocument: ReactNativeDocument_default,
): ReadOnlyText_default
declare type createPublicTextInstanceT = typeof createPublicTextInstance
declare type CursorValue = "auto" | "pointer"
declare type DataDetectorTypesType =
  | "address"
  | "all"
  | "calendarEvent"
  | "flightNumber"
  | "link"
  | "lookupSuggestion"
  | "none"
  | "phoneNumber"
  | "trackingNumber"
declare type decay = typeof decay
declare type DecayAnimationConfig = Readonly<
  AnimationConfig & {
    deceleration?: number
    velocity:
      | number
      | {
          readonly x: number
          readonly y: number
        }
  }
>
declare type DecelerationRateType = "fast" | "normal" | number
declare type DefaultSectionT = {
  [key: string]: any
}
declare type DefaultTypes = boolean | number | ReadonlyArray<string> | string
declare type DefaultVirtualizedSectionT = {
  [key: string]: any
  data: any
}
declare type delay = typeof delay
declare interface DEPRECATED_RCTExport<T extends void = void> {
  readonly getConstants?: () => {}
}
declare type DeterminateProgressBarAndroidStyleAttrProp = {
  indeterminate: false
  progress: number
  styleAttr: "Horizontal"
}
declare type DeviceEventEmitter = typeof DeviceEventEmitter
declare type DeviceInfo = typeof DeviceInfo
declare type DeviceInfoConstants = {
  readonly Dimensions: DimensionsPayload
  readonly isEdgeToEdge?: boolean
  readonly isIPhoneX_deprecated?: boolean
}
declare type DevMenu = typeof DevMenu
declare type DevMenuStatic = {
  show(): void
}
declare type DevSettings = typeof DevSettings
declare type DialogAction = string
declare type DialogButtonKey = number
declare type DialogOptions = {
  buttonNegative?: string
  buttonNeutral?: string
  buttonPositive?: string
  cancelable?: boolean
  items?: Array<string>
  message?: string
  title?: string
}
declare type diffClamp = typeof diffClamp
declare class Dimensions {
  static addEventListener(type: "change", handler: Function): EventSubscription
  static get(dim: string): DisplayMetrics | DisplayMetricsAndroid
  static set(dims: Readonly<DimensionsPayload>): void
}
declare type DimensionsPayload = {
  screen?: DisplayMetrics
  screenPhysicalPixels?: DisplayMetricsAndroid
  window?: DisplayMetrics
  windowPhysicalPixels?: DisplayMetricsAndroid
}
declare type DimensionValue = "auto" | null | number | string
declare type DirectEventHandler<T, PaperName extends never | string = never> = (
  event: NativeSyntheticEvent<T>,
) => Promise<void> | void
declare type DirectEventProps = {
  readonly onAccessibilityAction?: (event: AccessibilityActionEvent) => unknown
  readonly onAccessibilityEscape?: () => unknown
  readonly onAccessibilityTap?: () => unknown
  readonly onLayout?: (event: LayoutChangeEvent) => unknown
  readonly onMagicTap?: () => unknown
}
declare type DisplayMetrics = {
  fontScale: number
  height: number
  scale: number
  width: number
}
declare type DisplayMetricsAndroid = {
  densityDpi: number
  fontScale: number
  height: number
  scale: number
  width: number
}
declare type DisplayModeType = symbol & {
  __DisplayModeType__: string
}
declare type divide = typeof divide
declare class DOMRect_default extends DOMRectReadOnly_default {
  static fromRect(rect?: DOMRectInit | null | undefined): DOMRect_default
  get height(): number
  set height(height: null | number | undefined)
  get width(): number
  set width(width: null | number | undefined)
  get x(): number
  set x(x: null | number | undefined)
  get y(): number
  set y(y: null | number | undefined)
}
declare interface DOMRectInit {
  height?: number
  width?: number
  x?: number
  y?: number
}
declare class DOMRectReadOnly_default {
  get bottom(): number
  constructor(
    x: null | number | undefined,
    y: null | number | undefined,
    width: null | number | undefined,
    height: null | number | undefined,
  )
  static fromRect(
    rect?: DOMRectInit | null | undefined,
  ): DOMRectReadOnly_default
  get height(): number
  get left(): number
  get right(): number
  toJSON(): {
    bottom: number
    height: number
    left: number
    right: number
    top: number
    width: number
    x: number
    y: number
  }
  get top(): number
  get width(): number
  get x(): number
  get y(): number
}
declare type Double = number
declare type DrawerLayoutAndroid = typeof DrawerLayoutAndroid
declare class DrawerLayoutAndroid_default
  extends React.Component<DrawerLayoutAndroidProps, DrawerLayoutAndroidState>
  implements DrawerLayoutAndroidMethods
{
  blur(): void
  closeDrawer(): void
  focus(): void
  measure(callback: MeasureOnSuccessCallback): void
  measureInWindow(callback: MeasureInWindowOnSuccessCallback): void
  measureLayout(
    relativeToNativeNode: number,
    onSuccess: MeasureLayoutOnSuccessCallback,
    onFail?: () => void,
  ): void
  openDrawer(): void
  render(): React.ReactNode
  setNativeProps(nativeProps: Object): void
}
declare interface DrawerLayoutAndroidMethods {
  blur(): void
  closeDrawer(): void
  focus(): void
  measure(callback: MeasureOnSuccessCallback): void
  measureInWindow(callback: MeasureInWindowOnSuccessCallback): void
  measureLayout(
    relativeToNativeNode: number,
    onSuccess: MeasureLayoutOnSuccessCallback,
    onFail?: () => void,
  ): void
  openDrawer(): void
  setNativeProps(nativeProps: Object): void
}
declare type DrawerLayoutAndroidProps = Readonly<
  ViewProps & {
    drawerBackgroundColor?: ColorValue
    drawerLockMode?: "locked-closed" | "locked-open" | "unlocked"
    drawerPosition: ("left" | "right") | undefined
    drawerWidth?: number
    keyboardDismissMode?: "none" | "on-drag"
    onDrawerClose?: () => unknown
    onDrawerOpen?: () => unknown
    onDrawerSlide?: (event: DrawerSlideEvent) => unknown
    onDrawerStateChanged?: (state: DrawerStates) => unknown
    statusBarBackgroundColor?: ColorValue
    renderNavigationView: () => React.JSX.Element
  }
>
declare type DrawerLayoutAndroidState = {
  drawerOpened: boolean
}
declare type DrawerSlideEvent = NativeSyntheticEvent<{
  readonly offset: number
}>
declare type DrawerStates = "Dragging" | "Idle" | "Settling"
declare type DropShadowValue = {
  color?: ____ColorValue_Internal
  offsetX: number | string
  offsetY: number | string
  standardDeviation?: number | string
}
declare type DynamicColorIOS = typeof DynamicColorIOS
declare type DynamicColorIOSTuple = {
  dark: ColorValue
  highContrastDark?: ColorValue
  highContrastLight?: ColorValue
  light: ColorValue
}
declare type Easing = typeof Easing
declare type EasingFunction = (t: number) => number
declare type EdgeInsetsOrSizeProp = RectOrSize
declare type EdgeInsetsProp = Rect
declare type EdgeInsetsValue = {
  bottom: number
  left: number
  right: number
  top: number
}
declare type EmitterSubscription = EventSubscription
declare function endAsyncEvent(
  eventName: EventName,
  cookie: number,
  args?: EventArgs,
): void
declare type EndCallback = (result: EndResult) => void
declare function endEvent(args?: EventArgs): void
declare type EndResult = {
  finished: boolean
  offset?: number
  value?: number
}
declare type EnterKeyHintType = "done" | "go" | "next" | "search" | "send"
declare type EnterKeyHintTypeAndroid = "previous"
declare type EnterKeyHintTypeIOS = "enter"
declare type EnterKeyHintTypeOptions =
  | EnterKeyHintType
  | EnterKeyHintTypeAndroid
  | EnterKeyHintTypeIOS
declare type ErrorHandler = (error: unknown, isFatal: boolean) => void
declare type ErrorUtils = {
  applyWithGuard<TArgs extends ReadonlyArray<unknown>, TOut>(
    fun: Fn<TArgs, TOut>,
    context?: unknown,
    args?: TArgs | undefined,
    unused_onError?: null,
    unused_name?: string | undefined,
  ): TOut | undefined
  applyWithGuardIfNeeded<TArgs extends ReadonlyArray<unknown>, TOut>(
    fun: Fn<TArgs, TOut>,
    context?: unknown,
    args?: TArgs | undefined,
  ): TOut | undefined
  getGlobalHandler(): ErrorHandler
  guard<TArgs extends ReadonlyArray<unknown>, TOut>(
    fun: Fn<TArgs, TOut>,
    name?: string | undefined,
    context?: unknown,
  ): ((...$$REST$$: TArgs) => TOut | undefined) | undefined
  inGuard(): boolean
  reportError(error: unknown): void
  reportFatalError(error: unknown): void
  setGlobalHandler(fun: ErrorHandler): void
}
declare type event = typeof event
declare type EventArgs =
  | null
  | undefined
  | {
      [$$Key$$: string]: string
    }
declare type EventConfig<T> = {
  listener?: ($$PARAM_0$$: NativeSyntheticEvent<T>) => unknown
  platformConfig?: PlatformConfig
  useNativeDriver: boolean
}
declare type EventEmitter<T> = (
  handler: ($$PARAM_0$$: T) => Promise<void> | void,
) => EventSubscription
declare type EventHandlers = {
  readonly onBlur: (event: BlurEvent) => void
  readonly onClick: (event: GestureResponderEvent) => void
  readonly onFocus: (event: FocusEvent) => void
  readonly onMouseEnter?: (event: MouseEvent) => void
  readonly onMouseLeave?: (event: MouseEvent) => void
  readonly onPointerEnter?: (event: PointerEvent) => void
  readonly onPointerLeave?: (event: PointerEvent) => void
  readonly onPressIn: (event: any) => void
  readonly onPressOut: (event: any) => void
  readonly onResponderGrant: (event: GestureResponderEvent) => boolean | void
  readonly onResponderMove: (event: GestureResponderEvent) => void
  readonly onResponderRelease: (event: GestureResponderEvent) => void
  readonly onResponderTerminate: (event: GestureResponderEvent) => void
  readonly onResponderTerminationRequest: () => boolean
  readonly onStartShouldSetResponder: () => boolean
}
declare type EventName = (() => string) | string
declare interface EventSubscription {
  remove(): void
}
declare type ExceptionData = {
  componentStack: string | undefined
  extraData?: Object
  id: number
  isFatal: boolean
  message: string
  name: string | undefined
  originalMessage: string | undefined
  stack: Array<StackFrame>
}
declare type ExtendedExceptionData = ExceptionData & {
  isComponentError: boolean
}
declare type ExtrapolateType = "clamp" | "extend" | "identity"
declare type Extras = {
  [key: string]: ExtraValue
}
declare type ExtraValue = boolean | number | string
declare type FetchResult = {
  NewData: "UIBackgroundFetchResultNewData"
  NoData: "UIBackgroundFetchResultNoData"
  ResultFailed: "UIBackgroundFetchResultFailed"
}
declare class FillRateHelper_default {
  activate(): void
  static addListener(callback: ($$PARAM_0$$: FillRateInfo) => void): {
    remove: () => void
  }
  computeBlankness(
    props: CellMetricProps & {
      initialNumToRender?: number
    },
    cellsAroundViewport: {
      first: number
      last: number
    },
    scrollMetrics: {
      dOffset: number
      offset: number
      velocity: number
      visibleLength: number
    },
  ): number
  constructor(listMetrics: ListMetricsAggregator_default)
  deactivateAndFlush(): void
  enabled(): boolean
  static setMinSampleCount(minSampleCount: number): void
  static setSampleRate(sampleRate: number): void
}
declare type FillRateHelperT = typeof FillRateHelper_default
declare type FillRateInfo = Info
declare type FilterFunction =
  | {
      blur: number | string
    }
  | {
      brightness: number | string
    }
  | {
      contrast: number | string
    }
  | {
      dropShadow: DropShadowValue | string
    }
  | {
      grayscale: number | string
    }
  | {
      hueRotate: number | string
    }
  | {
      invert: number | string
    }
  | {
      opacity: number | string
    }
  | {
      saturate: number | string
    }
  | {
      sepia: number | string
    }
declare type findNodeHandle = typeof findNodeHandle
declare class FlatList<ItemT = any> extends React.PureComponent<
  FlatListProps<ItemT>
> {
  componentDidUpdate(prevProps: FlatListProps<ItemT>): void
  constructor(props: FlatListProps<ItemT>)
  flashScrollIndicators(): void
  getNativeScrollRef():
    | (null | React.ComponentRef<ScrollViewNativeComponent> | undefined)
    | (null | React.ComponentRef<typeof View> | undefined)
  getScrollableNode(): any
  getScrollResponder(): null | ScrollResponderType | undefined
  recordInteraction(): void
  render(): React.ReactNode
  scrollToEnd(
    params?:
      | null
      | undefined
      | {
          animated?: boolean
        },
  ): void
  scrollToIndex(params: {
    animated?: boolean
    index: number
    viewOffset?: number
    viewPosition?: number
  }): void
  scrollToItem(params: {
    animated?: boolean
    item: ItemT
    viewOffset?: number
    viewPosition?: number
  }): void
  scrollToOffset(params: { animated?: boolean; offset: number }): void
  setNativeProps(props: { [$$Key$$: string]: unknown }): void
}
declare type FlatListBaseProps<ItemT> = RequiredFlatListProps<ItemT> &
  OptionalFlatListProps<ItemT>
declare type FlatListProps<ItemT> = Omit<
  VirtualizedListProps,
  | "data"
  | "getItem"
  | "getItemCount"
  | "getItemLayout"
  | "keyExtractor"
  | "renderItem"
> &
  FlatListBaseProps<ItemT>
declare type flatten = typeof flatten
declare type FlattenDepthLimiter = [void, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
declare function flattenStyle_default<
  TStyleProp extends ____DangerouslyImpreciseAnimatedStyleProp_Internal,
>(
  style: null | TStyleProp | undefined,
):
  | NonAnimatedNodeObject<____FlattenStyleProp_Internal<TStyleProp>>
  | null
  | undefined
declare type Float = number
declare type Fn<Args extends ReadonlyArray<unknown>, Return> = (
  ...$$REST$$: Args
) => Return
declare type FocusEvent = NativeSyntheticEvent<TargetedEvent>
declare type FocusEventProps = {
  readonly onBlur?: (event: BlurEvent) => void
  readonly onBlurCapture?: (event: BlurEvent) => void
  readonly onFocus?: (event: FocusEvent) => void
  readonly onFocusCapture?: (event: FocusEvent) => void
}
declare type FontVariant = ____FontVariant_Internal
declare type forkEvent = typeof forkEvent
declare function forkEventImpl(
  event: (AnimatedEvent | null | undefined) | (Function | null | undefined),
  listener: Function,
): AnimatedEvent | Function
declare class FormData_default {
  append(key: string, value: FormDataValue): void
  constructor()
  getAll(key: string): Array<FormDataValue>
  getParts(): Array<FormDataPart>
}
declare type FormDataPart =
  | {
      headers: Headers
      name?: string
      type?: string
      uri: string
    }
  | {
      headers: Headers
      string: string
    }
declare type FormDataValue =
  | string
  | {
      name?: string
      type?: string
      uri: string
    }
declare type GestureResponderEvent = ResponderSyntheticEvent<NativeTouchEvent>
declare type GestureResponderHandlerMethods = {
  onMoveShouldSetResponder: (event: GestureResponderEvent) => boolean
  onMoveShouldSetResponderCapture: (event: GestureResponderEvent) => boolean
  onResponderEnd: (event: GestureResponderEvent) => void
  onResponderGrant: (event: GestureResponderEvent) => boolean
  onResponderMove: (event: GestureResponderEvent) => void
  onResponderReject: (event: GestureResponderEvent) => void
  onResponderRelease: (event: GestureResponderEvent) => void
  onResponderStart: (event: GestureResponderEvent) => void
  onResponderTerminate: (event: GestureResponderEvent) => void
  onResponderTerminationRequest: (event: GestureResponderEvent) => boolean
  onStartShouldSetResponder: (event: GestureResponderEvent) => boolean
  onStartShouldSetResponderCapture: (event: GestureResponderEvent) => boolean
}
declare type GestureResponderHandlers = {
  readonly onMoveShouldSetResponder?: (e: GestureResponderEvent) => boolean
  readonly onMoveShouldSetResponderCapture?: (
    e: GestureResponderEvent,
  ) => boolean
  readonly onResponderEnd?: (e: GestureResponderEvent) => void
  readonly onResponderGrant?: (e: GestureResponderEvent) => boolean | void
  readonly onResponderMove?: (e: GestureResponderEvent) => void
  readonly onResponderReject?: (e: GestureResponderEvent) => void
  readonly onResponderRelease?: (e: GestureResponderEvent) => void
  readonly onResponderStart?: (e: GestureResponderEvent) => void
  readonly onResponderTerminate?: (e: GestureResponderEvent) => void
  readonly onResponderTerminationRequest?: (e: GestureResponderEvent) => boolean
  readonly onStartShouldSetResponder?: (e: GestureResponderEvent) => boolean
  readonly onStartShouldSetResponderCapture?: (
    e: GestureResponderEvent,
  ) => boolean
}
declare function get<Config extends {}>(
  name: string,
  viewConfigProvider: () => PartialViewConfig,
): HostComponent<Config>
declare function get_2<T extends TurboModule>(
  name: string,
): null | T | undefined
declare function getAppKeys(): ReadonlyArray<string>
declare function getColorScheme(): ColorSchemeName | null | undefined
declare function getEnforcing<T extends TurboModule>(name: string): T
declare function getRegistry(): Registry
declare function getRunnable(appKey: string): null | Runnable | undefined
declare function getSectionKeys(): ReadonlyArray<string>
declare function getSections(): Runnables
declare function getWithFallback_DEPRECATED<Config extends {}>(
  name: string,
  viewConfigProvider: () => PartialViewConfig,
): React.ComponentType<Config>
declare type hairlineWidth = typeof hairlineWidth
declare type Handle = number
declare type Headers = {
  [name: string]: string
}
declare type HeadlessTask = (taskData: any) => Promise<void>
declare type HostComponent<Config extends {}> = (
  props: Omit<Config, "ref"> & {
    ref?: React.Ref<HostInstance>
  },
) => React.ReactNode
declare type HostInstance = ReactNativeElement_default
declare class HTMLCollection_default<T> implements Iterable<T>, ArrayLike_2<T> {
  [index: number]: T
  readonly length: number
  item(index: number): null | T
  namedItem(name: string): null | T
  [Symbol.iterator](): Iterator<T>
}
declare type I18nManager = typeof I18nManager
declare type I18nManagerConstants = {
  doLeftAndRightSwapInRTL: boolean
  isRTL: boolean
  localeIdentifier?: string
}
declare interface IEventEmitter<
  TEventToArgsMap extends Readonly<
    Record<string, ReadonlyArray<UnsafeEventObject>>
  >,
> {
  addListener<TEvent extends keyof TEventToArgsMap>(
    eventType: TEvent,
    listener: (...args: TEventToArgsMap[TEvent]) => unknown,
    context?: unknown,
  ): EventSubscription
  emit<TEvent extends keyof TEventToArgsMap>(
    eventType: TEvent,
    ...args: TEventToArgsMap[TEvent]
  ): void
  listenerCount<TEvent extends keyof TEventToArgsMap>(eventType: TEvent): number
  removeAllListeners<TEvent extends keyof TEventToArgsMap>(
    eventType?: TEvent | undefined,
  ): void
}
declare type IgnorePattern = RegExp | string
declare interface ILogBox {
  addConsoleLog(level: "error" | "warn", ...args: Array<unknown>): void
  addException(error: ExtendedExceptionData): void
  addLog(log: LogData): void
  clearAllLogs(): void
  ignoreAllLogs(value?: boolean): void
  ignoreLogs($$PARAM_0$$: ReadonlyArray<IgnorePattern>): void
  install(): void
  isInstalled(): boolean
  uninstall(): void
}
declare type Image = typeof Image
declare type ImageAndroid = AbstractImageAndroid & ImageComponentStaticsAndroid
declare class ImageBackground extends React.Component<ImageBackgroundProps> {
  render(): React.ReactNode
  setNativeProps(props: {}): void
}
declare type ImageBackgroundProps = Readonly<
  Omit<ImageProps, "children" | "style"> & {
    children?: React.ReactNode
    imageRef?: React.Ref<React.ComponentRef<ImageType>>
    imageStyle?: ImageStyleProp
    style?: ViewStyleProp
  }
>
declare type ImageComponentStaticsAndroid = Readonly<
  ImageComponentStaticsIOS & {
    abortPrefetch(requestId: number): void
  }
>
declare type ImageComponentStaticsIOS = {
  getSize(uri: string): Promise<ImageSize>
  getSize(
    uri: string,
    success: (width: number, height: number) => void,
    failure?: (error: unknown) => void,
  ): void
  getSizeWithHeaders(
    uri: string,
    headers: {
      [$$Key$$: string]: string
    },
  ): Promise<ImageSize>
  getSizeWithHeaders(
    uri: string,
    headers: {
      [$$Key$$: string]: string
    },
    success: (width: number, height: number) => void,
    failure?: (error: unknown) => void,
  ): void
  prefetch(url: string): Promise<boolean>
  prefetchWithMetadata(
    url: string,
    queryRootName: string,
    rootTag?: RootTag | undefined,
  ): Promise<boolean>
  queryCache(urls: Array<string>): Promise<{
    [url: string]: "disk" | "disk/memory" | "memory"
  }>
  resolveAssetSource(source: ImageSource): ImageResolvedAssetSource | undefined
}
declare type ImageErrorEvent = NativeSyntheticEvent<
  Readonly<ImageErrorEventData>
>
declare type ImageErrorEventData = {
  error: string
}
declare type ImageIOS = AbstractImageIOS & ImageComponentStaticsIOS
declare type ImageLoadEvent = NativeSyntheticEvent<Readonly<ImageLoadEventData>>
declare type ImageLoadEventData = {
  source: {
    height: number
    uri: string
    width: number
  }
}
declare type ImageProgressEventDataIOS = {
  loaded: number
  total: number
}
declare type ImageProgressEventIOS = NativeSyntheticEvent<
  Readonly<ImageProgressEventDataIOS>
>
declare type ImageProps = Readonly<
  ImagePropsIOS &
    ImagePropsAndroid &
    ImagePropsBase & {
      style?: ImageStyleProp
    }
>
declare type ImagePropsAndroid = {
  readonly fadeDuration?: number
  readonly loadingIndicatorSource?: number | Readonly<ImageURISource>
  readonly progressiveRenderingEnabled?: boolean
  readonly resizeMethod?: "auto" | "none" | "resize" | "scale"
  readonly resizeMultiplier?: number
}
declare type ImagePropsBase = Readonly<
  Omit<
    Omit<ViewProps, "style">,
    | "accessibilityLabel"
    | "accessible"
    | "aria-label"
    | "aria-labelledby"
    | "children"
    | "onLayout"
    | "testID"
  > & {
    accessibilityLabel?: string
    accessible?: boolean
    alt?: string
    "aria-label"?: string
    "aria-labelledby"?: string
    blurRadius?: number
    capInsets?: EdgeInsetsProp
    children?: never
    crossOrigin?: "anonymous" | "use-credentials"
    height?: number
    internal_analyticTag?: string
    onError?: (event: ImageErrorEvent) => void
    onLayout?: (event: LayoutChangeEvent) => unknown
    onLoad?: (event: ImageLoadEvent) => void
    onLoadEnd?: () => void
    onLoadStart?: () => void
    referrerPolicy?:
      | "no-referrer-when-downgrade"
      | "no-referrer"
      | "origin-when-cross-origin"
      | "origin"
      | "same-origin"
      | "strict-origin-when-cross-origin"
      | "strict-origin"
      | "unsafe-url"
    resizeMode?: ImageResizeMode
    source?: ImageSource
    src?: string
    srcSet?: string
    testID?: string
    tintColor?: ColorValue
    width?: number
  }
>
declare type ImagePropsIOS = {
  readonly defaultSource?: ImageSource
  readonly onPartialLoad?: () => void
  readonly onProgress?: (event: ImageProgressEventIOS) => void
}
declare type ImageRequireSource = number
declare type ImageResizeMode =
  | "center"
  | "contain"
  | "cover"
  | "none"
  | "repeat"
  | "stretch"
declare type ImageResolvedAssetSource = ResolvedAssetSource
declare type ImageSize = {
  height: number
  width: number
}
declare type ImageSource =
  | ImageRequireSource
  | ImageURISource
  | ReadonlyArray<ImageURISource>
declare type ImageSourcePropType = ImageSource
declare type ImageStyle = ____ImageStyle_Internal
declare type ImageStyleProp = ____ImageStyleProp_Internal
declare type ImageType = ImageAndroid | ImageIOS
declare interface ImageURISource {
  readonly body?: string
  readonly bundle?: string
  readonly cache?: "default" | "force-cache" | "only-if-cached" | "reload"
  readonly headers?: {
    [$$Key$$: string]: string
  }
  readonly height?: number
  readonly method?: string
  readonly scale?: number
  readonly uri?: string
  readonly width?: number
}
declare type IndeterminateProgressBarAndroidStyleAttrProp = {
  indeterminate: true
  styleAttr:
    | "Horizontal"
    | "Inverse"
    | "Large"
    | "LargeInverse"
    | "Normal"
    | "Small"
    | "SmallInverse"
}
declare type IndicatorSize = "large" | "small" | number
declare class Info {
  any_blank_count: number
  any_blank_ms: number
  any_blank_speed_sum: number
  mostly_blank_count: number
  mostly_blank_ms: number
  pixels_blank: number
  pixels_sampled: number
  pixels_scrolled: number
  sample_count: number
  total_time_spent: number
}
declare type InnerViewInstance = React.ComponentRef<typeof View>
declare type InputAccessoryView = typeof InputAccessoryView
declare type InputAccessoryViewProps = {
  readonly backgroundColor?: ColorValue
  readonly children: React.ReactNode
  readonly nativeID?: string
  readonly style?: ViewStyleProp
}
declare type InputModeOptions =
  | "decimal"
  | "email"
  | "none"
  | "numeric"
  | "search"
  | "tel"
  | "text"
  | "url"
declare type InputValue =
  | (ColorValue | RgbaAnimatedValue | RgbaValue)
  | null
  | undefined
declare type Insets = Rect
declare type InstanceHandle =
  | InternalInstanceHandle
  | ReactNativeDocumentElementInstanceHandle
  | ReactNativeDocumentInstanceHandle
declare type Int32 = number
declare type InteractionManager = typeof InteractionManager
declare type InternalInstanceHandle = symbol & {
  __InternalInstanceHandle__: string
}
declare type InternalTextInput = (
  props: TextInputProps & {
    ref?: React.Ref<TextInputInstance>
  },
) => React.ReactNode
declare type InterpolationConfigType<OutputT extends number | string> =
  Readonly<
    AnimatedNodeConfig & {
      extrapolate?: ExtrapolateType
      extrapolateLeft?: ExtrapolateType
      extrapolateRight?: ExtrapolateType
      inputRange: ReadonlyArray<number>
      outputRange: ReadonlyArray<OutputT>
      easing?: (input: number) => number
    }
  >
declare type IOSKeyboardEvent = Readonly<
  BaseKeyboardEvent & {
    isEventFromThisApp: boolean
    startCoordinates: KeyboardMetrics
  }
>
declare type IOSPlatform = {
  OS: "ios"
  select: <T>(spec: PlatformSelectSpec<T>) => T
  get constants(): {
    forceTouchAvailable: boolean
    interfaceIdiom: string
    isDisableAnimations?: boolean
    isMacCatalyst?: boolean
    isTesting: boolean
    osVersion: string
    reactNativeVersion: {
      major: number
      minor: number
      patch: number
      prerelease: string | undefined
    }
    systemName: string
  }
  get isDisableAnimations(): boolean
  get isMacCatalyst(): boolean
  get isPad(): boolean
  get isTesting(): boolean
  get isTV(): boolean
  get isVision(): boolean
  get Version(): string
}
declare interface IPerformanceLogger {
  addTimespan(
    key: string,
    startTime: number,
    endTime: number,
    startExtras?: Extras,
    endExtras?: Extras,
  ): void
  append(logger: IPerformanceLogger): void
  clear(): void
  clearCompleted(): void
  close(): void
  currentTimestamp(): number
  getExtras(): {
    readonly [key: string]: ExtraValue | undefined
  }
  getPointExtras(): {
    readonly [key: string]: Extras | undefined
  }
  getPoints(): {
    readonly [key: string]: number | undefined
  }
  getTimespans(): {
    readonly [key: string]: Timespan | undefined
  }
  hasTimespan(key: string): boolean
  isClosed(): boolean
  logEverything(): void
  markPoint(key: string, timestamp?: number, extras?: Extras): void
  removeExtra(key: string): ExtraValue | undefined
  setExtra(key: string, value: ExtraValue): void
  startTimespan(key: string, timestamp?: number, extras?: Extras): void
  stopTimespan(key: string, timestamp?: number, extras?: Extras): void
}
declare function isEnabled(): boolean
declare type Item = any
declare type Keyboard = typeof Keyboard
declare class KeyboardAvoidingView extends React.Component<
  KeyboardAvoidingViewProps,
  KeyboardAvoidingViewState
> {
  viewRef: {
    current: null | React.ComponentRef<typeof View>
  }
  componentDidMount(): void
  componentDidUpdate(
    _: KeyboardAvoidingViewProps,
    prevState: KeyboardAvoidingViewState,
  ): void
  componentWillUnmount(): void
  constructor(props: KeyboardAvoidingViewProps)
  render(): React.ReactNode
}
declare type KeyboardAvoidingViewProps = Readonly<
  Omit<ViewProps, "enabled"> & {
    behavior?: "height" | "padding" | "position"
    contentContainerStyle?: ViewStyleProp
    enabled?: boolean
    keyboardVerticalOffset?: number
  }
>
declare type KeyboardAvoidingViewState = {
  bottom: number
}
declare type KeyboardEvent = AndroidKeyboardEvent | IOSKeyboardEvent
declare type KeyboardEventDefinitions = {
  keyboardDidChangeFrame: [KeyboardEvent]
  keyboardDidHide: [KeyboardEvent]
  keyboardDidShow: [KeyboardEvent]
  keyboardWillChangeFrame: [KeyboardEvent]
  keyboardWillHide: [KeyboardEvent]
  keyboardWillShow: [KeyboardEvent]
}
declare type KeyboardEventEasing =
  | "easeIn"
  | "easeInEaseOut"
  | "easeOut"
  | "keyboard"
  | "linear"
declare type KeyboardEventName = keyof KeyboardEventDefinitions
declare class KeyboardImpl {
  addListener<K extends keyof KeyboardEventDefinitions>(
    eventType: K,
    listener: (...$$REST$$: KeyboardEventDefinitions[K]) => unknown,
    context?: unknown,
  ): EventSubscription
  constructor()
  dismiss(): void
  isVisible(): boolean
  metrics(): KeyboardMetrics | null | undefined
  removeAllListeners<K extends keyof KeyboardEventDefinitions>(
    eventType: K | null | undefined,
  ): void
  scheduleLayoutAnimation(event: KeyboardEvent): void
}
declare type KeyboardMetrics = {
  readonly height: number
  readonly screenX: number
  readonly screenY: number
  readonly width: number
}
declare type KeyboardType =
  | "decimal-pad"
  | "default"
  | "email-address"
  | "number-pad"
  | "numeric"
  | "phone-pad"
  | "url"
declare type KeyboardTypeAndroid = "visible-password"
declare type KeyboardTypeIOS =
  | "ascii-capable-number-pad"
  | "ascii-capable"
  | "name-phone-pad"
  | "numbers-and-punctuation"
  | "twitter"
  | "web-search"
declare type KeyboardTypeOptions =
  | KeyboardType
  | KeyboardTypeAndroid
  | KeyboardTypeIOS
declare function keyExtractor(item: any, index: number): string
declare type KeysOfUnion<T> = T extends any ? keyof T : never
declare type LayoutAnimation = typeof LayoutAnimation
declare type LayoutAnimationAnim = {
  readonly delay?: number
  readonly duration?: number
  readonly initialVelocity?: number
  readonly property?: LayoutAnimationProperty
  readonly springDamping?: number
  readonly type?: LayoutAnimationType
}
declare type LayoutAnimationConfig = {
  readonly create?: LayoutAnimationAnim
  readonly delete?: LayoutAnimationAnim
  readonly duration: number
  readonly update?: LayoutAnimationAnim
}
declare type LayoutAnimationProperties = Readonly<{
  [prop in LayoutAnimationProperty]: prop
}>
declare type LayoutAnimationProperty =
  | "opacity"
  | "scaleX"
  | "scaleXY"
  | "scaleY"
declare type LayoutAnimationType =
  | "easeIn"
  | "easeInEaseOut"
  | "easeOut"
  | "keyboard"
  | "linear"
  | "spring"
declare type LayoutAnimationTypes = Readonly<{
  [type in LayoutAnimationType]: type
}>
declare type LayoutChangeEvent = NativeSyntheticEvent<{
  readonly layout: LayoutRectangle
}>
declare type LayoutConformanceProps = {
  readonly children: React.ReactNode
  readonly mode: "compatibility" | "strict"
}
declare type LayoutRectangle = {
  readonly height: number
  readonly width: number
  readonly x: number
  readonly y: number
}
declare interface LegacyHostInstanceMethods {
  blur(): void
  focus(): void
  measure(callback: MeasureOnSuccessCallback): void
  measureInWindow(callback: MeasureInWindowOnSuccessCallback): void
  measureLayout(
    relativeToNativeNode: HostInstance | number,
    onSuccess: MeasureLayoutOnSuccessCallback,
    onFail?: () => void,
  ): void
  setNativeProps(nativeProps: {}): void
}
declare type Linking = typeof Linking
declare type LinkingEventDefinitions = {
  url: [
    {
      url: string
    },
  ]
}
declare class LinkingImpl extends NativeEventEmitter<LinkingEventDefinitions> {
  addEventListener<K extends keyof LinkingEventDefinitions>(
    eventType: K,
    listener: (...$$REST$$: LinkingEventDefinitions[K]) => unknown,
  ): EventSubscription
  canOpenURL(url: string): Promise<boolean>
  constructor()
  getInitialURL(): Promise<null | string | undefined>
  openSettings(): Promise<void>
  openURL(url: string): Promise<void>
  sendIntent(
    action: string,
    extras?: Array<{
      key: string
      value: boolean | number | string
    }>,
  ): Promise<void>
}
declare class ListMetricsAggregator_default {
  cartesianOffset(flowRelativeOffset: number): number
  flowRelativeOffset(
    layout: LayoutRectangle,
    referenceContentLength?: null | number | undefined,
  ): number
  getAverageCellLength(): number
  getCellMetrics(
    index: number,
    props: CellMetricProps,
  ): CellMetrics | null | undefined
  getCellMetricsApprox(index: number, props: CellMetricProps): CellMetrics
  getCellOffsetApprox(index: number, props: CellMetricProps): number
  getContentLength(): number
  getHighestMeasuredCellIndex(): number
  hasContentLength(): boolean
  notifyCellLayout($$PARAM_0$$: {
    cellIndex: number
    cellKey: string
    layout: LayoutRectangle
    orientation: ListOrientation
  }): boolean
  notifyCellUnmounted(cellKey: string): void
  notifyListContentLayout($$PARAM_0$$: {
    layout: {
      readonly height: number
      readonly width: number
    }
    orientation: ListOrientation
  }): void
}
declare type ListOrientation = {
  horizontal: boolean
  rtl: boolean
}
declare type ListRenderItem<ItemT> = (
  info: ListRenderItemInfo<ItemT>,
) => React.ReactNode
declare type ListRenderItemInfo<ItemT> = {
  index: number
  item: ItemT
  separators: Separators
}
declare type ListViewToken = {
  index: number | undefined
  isViewable: boolean
  item: any
  key: string
  section?: any
}
declare type LogBox = typeof LogBox
declare type LogData = {
  readonly category: Category
  readonly componentStack: ComponentStack
  readonly componentStackType: ComponentStackType | null
  readonly level: LogLevel
  readonly message: Message
  readonly stack?: string
}
declare type LogLevel = "error" | "fatal" | "syntax" | "warn"
declare type loop = typeof loop
declare type LoopAnimationConfig = {
  iterations: number
  resetBeforeIteration?: boolean
}
declare type LooseOmit<O extends {}, K extends keyof any> = Pick<
  O,
  Exclude<keyof O, K>
>
declare type MacOSPlatform = {
  OS: "macos"
  select: <T>(spec: PlatformSelectSpec<T>) => T
  get constants(): {
    isTesting: boolean
    osVersion: string
    reactNativeVersion: {
      major: number
      minor: number
      patch: number
      prerelease: number | undefined
    }
    systemName: string
  }
  get isDisableAnimations(): boolean
  get isTesting(): boolean
  get isTV(): boolean
  get isVision(): boolean
  get Version(): string
}
declare type Mapping =
  | AnimatedValue_default
  | AnimatedValueXY_default
  | {
      [key: string]: Mapping
    }
declare type MaximumOneOf<T extends {}> = {
  [K in keyof T]: { [P in keyof T]?: P extends K ? T[P] : never }
}[keyof { [K in keyof T]: { [P in keyof T]?: P extends K ? T[P] : never } }]
declare type MeasureInWindowOnSuccessCallback = (
  x: number,
  y: number,
  width: number,
  height: number,
) => void
declare type MeasureLayoutOnSuccessCallback = (
  left: number,
  top: number,
  width: number,
  height: number,
) => void
declare type MeasureOnSuccessCallback = (
  x: number,
  y: number,
  width: number,
  height: number,
  pageX: number,
  pageY: number,
) => void
declare type MergeUnion<T> = { [K in KeysOfUnion<T>]?: ValueOfUnion<T, K> }
declare type Message = {
  readonly content: string
  readonly substitutions: ReadonlyArray<{
    readonly length: number
    readonly offset: number
  }>
}
declare type Modal = typeof Modal
declare type ModalBaseProps = {
  animated?: boolean
  animationType?: "fade" | "none" | "slide"
  backdropColor?: ColorValue
  modalRef?: React.Ref<PublicModalInstance>
  onRequestClose?: DirectEventHandler<null>
  onShow?: DirectEventHandler<null>
  transparent?: boolean
  visible?: boolean
}
declare type ModalProps = ModalBaseProps &
  ModalPropsIOS &
  ModalPropsAndroid &
  ViewProps
declare type ModalPropsAndroid = {
  hardwareAccelerated?: boolean
  navigationBarTranslucent?: boolean
  statusBarTranslucent?: boolean
}
declare type ModalPropsIOS = {
  allowSwipeDismissal?: boolean
  onDismiss?: () => void
  onOrientationChange?: DirectEventHandler<OrientationChangeEvent>
  presentationStyle?:
    | "formSheet"
    | "fullScreen"
    | "overFullScreen"
    | "pageSheet"
  supportedOrientations?: ReadonlyArray<
    | "landscape-left"
    | "landscape-right"
    | "landscape"
    | "portrait-upside-down"
    | "portrait"
  >
}
declare type ModalRefProps = {
  readonly ref?: React.Ref<PublicModalInstance>
}
declare type ModeChangeEvent = Readonly<
  Omit<NativeModeChangeEvent, "mode"> & {
    mode: VirtualViewMode
    target: HostInstance
  }
>
declare type Module = {}
declare type modulo = typeof modulo
declare type MouseEvent = NativeSyntheticEvent<{
  readonly clientX: number
  readonly clientY: number
  readonly pageX: number
  readonly pageY: number
  readonly timestamp: number
}>
declare type MouseEventProps = {
  readonly onMouseEnter?: (event: MouseEvent) => void
  readonly onMouseLeave?: (event: MouseEvent) => void
}
declare type multiply = typeof multiply
declare type NativeAppEventEmitter = typeof NativeAppEventEmitter
declare type NativeColorValue = symbol & {
  __NativeColorValue__: string
}
declare type NativeCommandsOptions<T = string> = {
  readonly supportedCommands: ReadonlyArray<T>
}
declare type NativeComponentOptions = {
  readonly excludedPlatforms?: ReadonlyArray<"android" | "iOS">
  readonly interfaceOnly?: boolean
  readonly paperComponentName?: string
  readonly paperComponentNameDeprecated?: string
}
declare namespace NativeComponentRegistry {
  export { setRuntimeConfigProvider, get, getWithFallback_DEPRECATED }
}
declare type NativeComponentType<T extends {}> = HostComponent<T>
declare type NativeDialogManagerAndroid = typeof NativeDialogManagerAndroid
declare class NativeEventEmitter<
  TEventToArgsMap extends Readonly<
    Record<string, ReadonlyArray<UnsafeNativeEventObject>>
  > = Readonly<Record<string, ReadonlyArray<UnsafeNativeEventObject>>>,
> implements IEventEmitter<TEventToArgsMap>
{
  addListener<TEvent extends keyof TEventToArgsMap>(
    eventType: TEvent,
    listener: (...args: TEventToArgsMap[TEvent]) => unknown,
    context?: unknown,
  ): EventSubscription
  constructor(nativeModule?: NativeModule | null | undefined)
  emit<TEvent extends keyof TEventToArgsMap>(
    eventType: TEvent,
    ...args: TEventToArgsMap[TEvent]
  ): void
  listenerCount<TEvent extends keyof TEventToArgsMap>(eventType: TEvent): number
  removeAllListeners<TEvent extends keyof TEventToArgsMap>(
    eventType?: null | TEvent | undefined,
  ): void
}
declare type NativeEventSubscription = EventSubscription
declare type NativeMeasureInWindowOnSuccessCallback = (
  x: number,
  y: number,
  width: number,
  height: number,
) => void
declare type NativeMeasureLayoutOnSuccessCallback = (
  left: number,
  top: number,
  width: number,
  height: number,
) => void
declare type NativeMeasureOnSuccessCallback = (
  x: number,
  y: number,
  width: number,
  height: number,
  pageX: number,
  pageY: number,
) => void
declare type NativeMethods = LegacyHostInstanceMethods
declare type NativeMethodsMixin = LegacyHostInstanceMethods
declare type NativeModeChangeEvent = {
  readonly mode: Int32
  readonly targetRect: {
    readonly height: Double
    readonly width: Double
    readonly x: Double
    readonly y: Double
  }
  readonly thresholdRect: {
    readonly height: Double
    readonly width: Double
    readonly x: Double
    readonly y: Double
  }
}
declare interface NativeModule {
  addListener(eventType: string): void
  removeListeners(count: number): void
}
declare type NativeModules = typeof NativeModules
declare interface NativeMouseEvent extends NativeUIEvent {
  readonly altKey: boolean
  readonly button: number
  readonly buttons: number
  readonly clientX: number
  readonly clientY: number
  readonly ctrlKey: boolean
  readonly metaKey: boolean
  readonly offsetX: number
  readonly offsetY: number
  readonly pageX: number
  readonly pageY: number
  readonly relatedTarget: HostInstance | null | number
  readonly screenX: number
  readonly screenY: number
  readonly shiftKey: boolean
  readonly x: number
  readonly y: number
}
declare interface NativePointerEvent extends NativeMouseEvent {
  readonly height: number
  readonly isPrimary: boolean
  readonly pointerId: number
  readonly pointerType: string
  readonly pressure: number
  readonly tangentialPressure: number
  readonly tiltX: number
  readonly tiltY: number
  readonly twist: number
  readonly width: number
}
declare type NativeResponseType = "base64" | "blob" | "text"
declare type NativeScrollEvent = {
  readonly contentInset: NativeScrollRectangle
  readonly contentOffset: NativeScrollPoint
  readonly contentSize: NativeScrollSize
  readonly layoutMeasurement: NativeScrollSize
  readonly responderIgnoreScroll?: boolean
  readonly targetContentOffset?: NativeScrollPoint
  readonly velocity?: NativeScrollVelocity
  readonly zoomScale?: number
}
declare type NativeScrollPoint = {
  readonly x: number
  readonly y: number
}
declare type NativeScrollRectangle = {
  readonly bottom: number
  readonly left: number
  readonly right: number
  readonly top: number
}
declare type NativeScrollSize = {
  readonly height: number
  readonly width: number
}
declare type NativeScrollVelocity = {
  readonly x: number
  readonly y: number
}
declare type NativeSwitchChangeEvent = {
  readonly target: Int32
  readonly value: boolean
}
declare type NativeSyntheticEvent<T> = {
  readonly bubbles: boolean | undefined
  readonly cancelable: boolean | undefined
  readonly currentTarget: HostInstance | number
  readonly defaultPrevented: boolean | undefined
  readonly dispatchConfig: {
    readonly registrationName: string
  }
  readonly eventPhase: number | undefined
  readonly isTrusted: boolean | undefined
  readonly nativeEvent: T
  readonly target: (number | undefined) | HostInstance
  readonly timeStamp: number
  readonly type: string | undefined
  readonly isDefaultPrevented: () => boolean
  readonly isPropagationStopped: () => boolean
  readonly persist: () => void
  readonly preventDefault: () => void
  readonly stopPropagation: () => void
}
declare type NativeText = typeof NativeText
declare type NativeTextProps = Readonly<
  Omit<TextProps, "selectionColor"> & {
    isHighlighted?: boolean
    isPressable?: boolean
    onClick?: (event: GestureResponderEvent) => unknown
    selectionColor?: ProcessedColorValue
  }
>
declare type NativeTouchEvent = {
  readonly changedTouches: ReadonlyArray<NativeTouchEvent>
  readonly force?: number
  readonly identifier: number
  readonly locationX: number
  readonly locationY: number
  readonly pageX: number
  readonly pageY: number
  readonly target: number | undefined
  readonly timestamp: number
  readonly touches: ReadonlyArray<NativeTouchEvent>
}
declare type NativeType = HostComponent<AndroidSwitchNativeProps>
declare interface NativeUIEvent {
  readonly detail: number
}
declare type NativeVirtualText = typeof NativeVirtualText
declare type Networking = typeof Networking
declare class NodeList_default<T> implements Iterable<T>, ArrayLike_2<T> {
  [index: number]: T
  readonly length: number
  entries(): Iterator<[number, T]>
  forEach<ThisType>(
    callbackFn: (
      value: T,
      index: number,
      array: NodeList_default<T>,
    ) => unknown,
    thisArg?: ThisType,
  ): void
  item(index: number): null | T
  keys(): Iterator<number>
  values(): Iterator<T>
  [Symbol.iterator](): Iterator<T>
}
declare type NonAnimatedNodeObject<TStyleProp> =
  TStyleProp extends AnimatedNode_default ? never : TStyleProp
declare type NonAnimatedProps =
  | "accessibilityLabel"
  | "disabled"
  | "innerViewRef"
  | "ref"
  | "scrollViewRef"
  | "testID"
declare type Nullable = null | void
declare type Omit_2<T, K> = T extends any ? Pick<T, Exclude<keyof T, K>> : T
declare type OnAnimationDidEndCallback = () => void
declare type OnAnimationDidFailCallback = () => void
declare type OpaqueColorValue = NativeColorValue
declare type OptionalFlatListProps<ItemT> = {
  columnWrapperStyle?: ViewStyleProp
  extraData?: any
  fadingEdgeLength?:
    | (number | undefined)
    | {
        end: number
        start: number
      }
  horizontal?: boolean
  initialNumToRender?: number
  initialScrollIndex?: number
  inverted?: boolean
  keyExtractor?: (item: ItemT, index: number) => string
  numColumns?: number
  removeClippedSubviews?: boolean
  renderItem?: ListRenderItem<ItemT>
  strictMode?: boolean
  getItemLayout?: (
    data: Readonly<ArrayLike<ItemT>> | undefined,
    index: number,
  ) => {
    index: number
    length: number
    offset: number
  }
}
declare type OptionalPlatformSelectSpec<T> = { [key in PlatformOSType]?: T }
declare type OptionalSectionListProps<ItemT, SectionT = DefaultSectionT> = {
  extraData?: any
  initialNumToRender?: number
  inverted?: boolean
  keyExtractor?: (item: ItemT, index: number) => string
  onEndReached?: (info: { distanceFromEnd: number }) => void
  removeClippedSubviews?: boolean
  renderItem?: SectionListRenderItem<ItemT, SectionT>
}
declare type OptionalVirtualizedListProps = {
  CellRendererComponent?: React.ComponentType<CellRendererProps<Item>>
  debug?: boolean
  disableVirtualization?: boolean
  extraData?: any
  horizontal?: boolean
  initialNumToRender?: number
  initialScrollIndex?: number
  inverted?: boolean
  ItemSeparatorComponent?: React.ComponentType<any> | React.JSX.Element
  keyExtractor?: (item: Item, index: number) => string
  ListEmptyComponent?: React.ComponentType<any> | React.JSX.Element
  ListFooterComponent?: React.ComponentType<any> | React.JSX.Element
  ListFooterComponentStyle?: StyleProp<ViewStyle>
  ListHeaderComponent?: React.ComponentType<any> | React.JSX.Element
  ListHeaderComponentStyle?: StyleProp<ViewStyle>
  ListItemComponent?: React.ComponentType<any> | React.JSX.Element
  maxToRenderPerBatch?: number
  onEndReached?: (info: { distanceFromEnd: number }) => void
  onEndReachedThreshold?: number
  onRefresh?: () => void
  onScrollToIndexFailed?: (info: {
    averageItemLength: number
    highestMeasuredFrameIndex: number
    index: number
  }) => void
  onStartReached?: (info: { distanceFromStart: number }) => void
  onStartReachedThreshold?: number
  onViewableItemsChanged?: (info: {
    changed: Array<ListViewToken>
    viewableItems: Array<ListViewToken>
  }) => void
  persistentScrollbar?: boolean
  progressViewOffset?: number
  refreshControl?: React.JSX.Element
  refreshing?: boolean
  removeClippedSubviews?: boolean
  renderItem?: ListRenderItem<Item>
  updateCellsBatchingPeriod?: number
  viewabilityConfig?: ViewabilityConfig
  viewabilityConfigCallbackPairs?: Array<ViewabilityConfigCallbackPair>
  windowSize?: number
  getItemLayout?: (
    data: any,
    index: number,
  ) => {
    index: number
    length: number
    offset: number
  }
  renderScrollComponent?: (props: ScrollViewProps) => React.JSX.Element
}
declare type OptionalVirtualizedSectionListProps<
  ItemT,
  SectionT = DefaultVirtualizedSectionT,
> = {
  onEndReached?: ($$PARAM_0$$: { distanceFromEnd: number }) => void
  renderSectionFooter?: (info: { section: SectionT }) => null | React.ReactNode
  renderSectionHeader?: (info: { section: SectionT }) => null | React.ReactNode
  SectionSeparatorComponent?: React.ComponentType<any>
  stickySectionHeadersEnabled?: boolean
  renderItem?: (info: {
    index: number
    item: ItemT
    section: SectionT
    separators: {
      highlight: () => void
      unhighlight: () => void
      updateProps: (select: "leading" | "trailing", newProps: Object) => void
    }
  }) => null | React.ReactNode
}
declare type OrientationChangeEvent = {
  readonly orientation: "landscape" | "portrait"
}
declare type PanResponder = typeof PanResponder
declare type PanResponderCallbacks = {
  readonly onMoveShouldSetPanResponder?: ActiveCallback
  readonly onMoveShouldSetPanResponderCapture?: ActiveCallback
  readonly onPanResponderEnd?: PassiveCallback
  readonly onPanResponderGrant?: ActiveCallback | PassiveCallback
  readonly onPanResponderMove?: PassiveCallback
  readonly onPanResponderReject?: PassiveCallback
  readonly onPanResponderRelease?: PassiveCallback
  readonly onPanResponderStart?: PassiveCallback
  readonly onPanResponderTerminate?: PassiveCallback
  readonly onPanResponderTerminationRequest?: ActiveCallback
  readonly onShouldBlockNativeResponder?: ActiveCallback
  readonly onStartShouldSetPanResponder?: ActiveCallback
  readonly onStartShouldSetPanResponderCapture?: ActiveCallback
}
declare type PanResponderGestureState = {
  dx: number
  dy: number
  moveX: number
  moveY: number
  numberActiveTouches: number
  stateID: number
  vx: number
  vy: number
  x0: number
  y0: number
}
declare type PanResponderInstance = ReturnType<
  (typeof PanResponder_default)["create"]
>
declare type parallel = typeof parallel
declare type ParallelConfig = {
  stopTogether?: boolean
}
declare type PartialViewConfig = {
  readonly bubblingEventTypes?: ViewConfig["bubblingEventTypes"]
  readonly directEventTypes?: ViewConfig["directEventTypes"]
  readonly supportsRawText?: boolean
  readonly uiViewClassName: string
  readonly validAttributes?: AttributeConfiguration
}
declare type PassiveCallback = (
  event: GestureResponderEvent,
  gestureState: PanResponderGestureState,
) => unknown
declare type PassThroughProps = {
  readonly passthroughAnimatedPropExplicitValues?: null | ViewProps
}
declare type PasswordRules = string
declare type Permission = PermissionsType[keyof PermissionsType]
declare type PermissionsAndroid = typeof PermissionsAndroid
declare class PermissionsAndroidImpl {
  PERMISSIONS: PermissionsType
  RESULTS: {
    readonly DENIED: "denied"
    readonly GRANTED: "granted"
    readonly NEVER_ASK_AGAIN: "never_ask_again"
  }
  check(permission: Permission): Promise<boolean>
  checkPermission(permission: Permission): Promise<boolean>
  request(
    permission: Permission,
    rationale?: Rationale,
  ): Promise<PermissionStatus>
  requestMultiple(
    permissions: Array<Permission>,
  ): Promise<{ [permission in Permission]: PermissionStatus }>
  requestPermission(
    permission: Permission,
    rationale?: Rationale,
  ): Promise<boolean>
}
declare type PermissionStatus = "denied" | "granted" | "never_ask_again"
declare type PermissionsType = {
  readonly ACCEPT_HANDOVER: "android.permission.ACCEPT_HANDOVER"
  readonly ACCESS_BACKGROUND_LOCATION: "android.permission.ACCESS_BACKGROUND_LOCATION"
  readonly ACCESS_COARSE_LOCATION: "android.permission.ACCESS_COARSE_LOCATION"
  readonly ACCESS_FINE_LOCATION: "android.permission.ACCESS_FINE_LOCATION"
  readonly ACCESS_MEDIA_LOCATION: "android.permission.ACCESS_MEDIA_LOCATION"
  readonly ACTIVITY_RECOGNITION: "android.permission.ACTIVITY_RECOGNITION"
  readonly ADD_VOICEMAIL: "com.android.voicemail.permission.ADD_VOICEMAIL"
  readonly ANSWER_PHONE_CALLS: "android.permission.ANSWER_PHONE_CALLS"
  readonly BLUETOOTH_ADVERTISE: "android.permission.BLUETOOTH_ADVERTISE"
  readonly BLUETOOTH_CONNECT: "android.permission.BLUETOOTH_CONNECT"
  readonly BLUETOOTH_SCAN: "android.permission.BLUETOOTH_SCAN"
  readonly BODY_SENSORS: "android.permission.BODY_SENSORS"
  readonly BODY_SENSORS_BACKGROUND: "android.permission.BODY_SENSORS_BACKGROUND"
  readonly CALL_PHONE: "android.permission.CALL_PHONE"
  readonly CAMERA: "android.permission.CAMERA"
  readonly GET_ACCOUNTS: "android.permission.GET_ACCOUNTS"
  readonly NEARBY_WIFI_DEVICES: "android.permission.NEARBY_WIFI_DEVICES"
  readonly POST_NOTIFICATIONS: "android.permission.POST_NOTIFICATIONS"
  readonly PROCESS_OUTGOING_CALLS: "android.permission.PROCESS_OUTGOING_CALLS"
  readonly READ_CALENDAR: "android.permission.READ_CALENDAR"
  readonly READ_CALL_LOG: "android.permission.READ_CALL_LOG"
  readonly READ_CONTACTS: "android.permission.READ_CONTACTS"
  readonly READ_EXTERNAL_STORAGE: "android.permission.READ_EXTERNAL_STORAGE"
  readonly READ_MEDIA_AUDIO: "android.permission.READ_MEDIA_AUDIO"
  readonly READ_MEDIA_IMAGES: "android.permission.READ_MEDIA_IMAGES"
  readonly READ_MEDIA_VIDEO: "android.permission.READ_MEDIA_VIDEO"
  readonly READ_MEDIA_VISUAL_USER_SELECTED: "android.permission.READ_MEDIA_VISUAL_USER_SELECTED"
  readonly READ_PHONE_NUMBERS: "android.permission.READ_PHONE_NUMBERS"
  readonly READ_PHONE_STATE: "android.permission.READ_PHONE_STATE"
  readonly READ_SMS: "android.permission.READ_SMS"
  readonly READ_VOICEMAIL: "com.android.voicemail.permission.READ_VOICEMAIL"
  readonly RECEIVE_MMS: "android.permission.RECEIVE_MMS"
  readonly RECEIVE_SMS: "android.permission.RECEIVE_SMS"
  readonly RECEIVE_WAP_PUSH: "android.permission.RECEIVE_WAP_PUSH"
  readonly RECORD_AUDIO: "android.permission.RECORD_AUDIO"
  readonly SEND_SMS: "android.permission.SEND_SMS"
  readonly USE_SIP: "android.permission.USE_SIP"
  readonly UWB_RANGING: "android.permission.UWB_RANGING"
  readonly WRITE_CALENDAR: "android.permission.WRITE_CALENDAR"
  readonly WRITE_CALL_LOG: "android.permission.WRITE_CALL_LOG"
  readonly WRITE_CONTACTS: "android.permission.WRITE_CONTACTS"
  readonly WRITE_EXTERNAL_STORAGE: "android.permission.WRITE_EXTERNAL_STORAGE"
  readonly WRITE_VOICEMAIL: "com.android.voicemail.permission.WRITE_VOICEMAIL"
}
declare class PixelRatio {
  static get(): number
  static getFontScale(): number
  static getPixelSizeForLayoutSize(layoutSize: number): number
  static roundToNearestPixel(layoutSize: number): number
  static startDetecting(): void
}
declare type Platform = typeof Platform
declare function PlatformColor(...names: Array<string>): ColorValue
declare type PlatformConfig = {}
declare type PlatformOSType =
  | "android"
  | "ios"
  | "macos"
  | "native"
  | "web"
  | "windows"
declare type PlatformSelectSpec<T> =
  | (Omit<OptionalPlatformSelectSpec<T>, "default"> & {
      default: T
    })
  | OptionalPlatformSelectSpec<T>
declare type PlatformType =
  | AndroidPlatform
  | IOSPlatform
  | MacOSPlatform
  | WebPlatform
  | WindowsPlatform
declare type PointerEvent = NativeSyntheticEvent<NativePointerEvent>
declare type PointerEventProps = {
  readonly onClick?: (event: PointerEvent) => void
  readonly onClickCapture?: (event: PointerEvent) => void
  readonly onGotPointerCapture?: (e: PointerEvent) => void
  readonly onGotPointerCaptureCapture?: (e: PointerEvent) => void
  readonly onLostPointerCapture?: (e: PointerEvent) => void
  readonly onLostPointerCaptureCapture?: (e: PointerEvent) => void
  readonly onPointerCancel?: (e: PointerEvent) => void
  readonly onPointerCancelCapture?: (e: PointerEvent) => void
  readonly onPointerDown?: (e: PointerEvent) => void
  readonly onPointerDownCapture?: (e: PointerEvent) => void
  readonly onPointerEnter?: (event: PointerEvent) => void
  readonly onPointerEnterCapture?: (event: PointerEvent) => void
  readonly onPointerLeave?: (event: PointerEvent) => void
  readonly onPointerLeaveCapture?: (event: PointerEvent) => void
  readonly onPointerMove?: (event: PointerEvent) => void
  readonly onPointerMoveCapture?: (event: PointerEvent) => void
  readonly onPointerOut?: (e: PointerEvent) => void
  readonly onPointerOutCapture?: (e: PointerEvent) => void
  readonly onPointerOver?: (e: PointerEvent) => void
  readonly onPointerOverCapture?: (e: PointerEvent) => void
  readonly onPointerUp?: (e: PointerEvent) => void
  readonly onPointerUpCapture?: (e: PointerEvent) => void
}
declare type PointProp = {
  readonly x: number
  readonly y: number
}
declare type PointValue = {
  x: number
  y: number
}
declare type PresentLocalNotificationDetails = {
  alertAction?: string
  alertBody: string
  alertTitle?: string
  applicationIconBadgeNumber?: number
  category?: string
  fireDate?: number
  isSilent?: boolean
  soundName?: string
  userInfo?: Object
}
declare class Pressability_default {
  configure(config: PressabilityConfig): void
  constructor(config: PressabilityConfig)
  getEventHandlers(): EventHandlers
  reset(): void
  static setLongPressDeactivationDistance(distance: number): void
}
declare type PressabilityConfig = {
  readonly android_disableSound?: boolean
  readonly blockNativeResponder?: boolean
  readonly cancelable?: boolean
  readonly delayHoverIn?: number
  readonly delayHoverOut?: number
  readonly delayLongPress?: number
  readonly delayPressIn?: number
  readonly delayPressOut?: number
  readonly disabled?: boolean
  readonly hitSlop?: RectOrSize
  readonly minPressDuration?: number
  readonly onBlur?: (event: BlurEvent) => unknown
  readonly onFocus?: (event: FocusEvent) => unknown
  readonly onHoverIn?: (event: MouseEvent) => unknown
  readonly onHoverOut?: (event: MouseEvent) => unknown
  readonly onLongPress?: (event: GestureResponderEvent) => unknown
  readonly onPress?: (event: GestureResponderEvent) => unknown
  readonly onPressIn?: (event: GestureResponderEvent) => unknown
  readonly onPressMove?: (event: GestureResponderEvent) => unknown
  readonly onPressOut?: (event: GestureResponderEvent) => unknown
  readonly onTVEvent?: (event: any) => void
  readonly pressRectOffset?: RectOrSize
}
declare type Pressable = typeof Pressable
declare type PressableAndroidRippleConfig = {
  borderless?: boolean
  color?: ColorValue
  foreground?: boolean
  radius?: number
}
declare type PressableBaseProps = {
  readonly android_disableSound?: boolean
  readonly android_ripple?: PressableAndroidRippleConfig
  readonly blockNativeResponder?: boolean
  readonly cancelable?: boolean
  readonly children?:
    | ((state: PressableStateCallbackType) => React.ReactNode)
    | React.ReactNode
  readonly delayHoverIn?: number
  readonly delayHoverOut?: number
  readonly delayLongPress?: number
  readonly disabled?: boolean
  readonly hitSlop?: RectOrSize
  readonly onHoverIn?: (event: MouseEvent) => unknown
  readonly onHoverOut?: (event: MouseEvent) => unknown
  readonly onLayout?: (event: LayoutChangeEvent) => unknown
  readonly onLongPress?: (event: GestureResponderEvent) => unknown
  readonly onPress?: (event: GestureResponderEvent) => unknown
  readonly onPressIn?: (event: GestureResponderEvent) => unknown
  readonly onPressMove?: (event: GestureResponderEvent) => unknown
  readonly onPressOut?: (event: GestureResponderEvent) => unknown
  readonly pressRetentionOffset?: RectOrSize
  readonly style?:
    | ((state: PressableStateCallbackType) => ViewStyleProp)
    | ViewStyleProp
  readonly testID?: string
  readonly testOnly_pressed?: boolean
}
declare type PressableProps = Readonly<
  Omit<ViewProps, "onMouseEnter" | "onMouseLeave"> &
    PressableBaseProps &
    TVProps
>
declare type PressableStateCallbackType = {
  readonly focused: boolean
  readonly pressed: boolean
}
declare type PressEventProps = {
  readonly onPressIn?: (e: RemotePressEvent) => void
  readonly onPressOut?: (e: RemotePressEvent) => void
}
declare type PressRetentionOffset = {
  readonly bottom: number
  readonly left: number
  readonly right: number
  readonly top: number
}
declare type Primitive = boolean | number | string | symbol | void
declare type processColor = typeof processColor
declare function processColor_default(
  color?: (ColorValue | number) | null | undefined,
): null | ProcessedColorValue | undefined
declare type ProcessedColorValue = NativeColorValue | number
declare type ProgressBarAndroid = typeof ProgressBarAndroid
declare type ProgressBarAndroidBaseProps = {
  readonly animating?: boolean
  readonly color?: ColorValue
  readonly testID?: string
}
declare type ProgressBarAndroidNativeComponentType =
  typeof $$ProgressBarAndroidNativeComponent
declare type ProgressBarAndroidProps =
  | Readonly<
      ViewProps &
        ProgressBarAndroidBaseProps &
        DeterminateProgressBarAndroidStyleAttrProp
    >
  | Readonly<
      ViewProps &
        ProgressBarAndroidBaseProps &
        IndeterminateProgressBarAndroidStyleAttrProp
    >
declare type PromiseTask = {
  name: string
  gen: () => Promise<void>
}
declare type Props = typeof ScrollView
declare type PublicModalInstance = HostInstance
declare type PublicRootInstance = symbol & {
  __PublicRootInstance__: string
}
declare interface PublicScrollViewInstance
  extends HostInstance,
    ScrollViewImperativeMethods {}
declare type PublicTextInstance = ReturnType<createPublicTextInstanceT>
declare interface PushNotification {
  finish(result: string): void
  getAlert(): (Object | undefined) | (string | undefined)
  getBadgeCount(): number | undefined
  getCategory(): string | undefined
  getContentAvailable(): ContentAvailable
  getData(): Object | undefined
  getMessage(): (Object | undefined) | (string | undefined)
  getSound(): string | undefined
  getThreadID(): string | undefined
}
declare type PushNotificationEventName = keyof {
  localNotification: string
  notification: string
  register: string
  registrationError: string
}
declare class PushNotificationIOS {
  static FetchResult: FetchResult
  static abandonPermissions(): void
  static addEventListener(
    type: PushNotificationEventName,
    handler: Function,
  ): void
  static cancelAllLocalNotifications(): void
  static cancelLocalNotifications(userInfo: Object): void
  static checkPermissions(
    callback: (permissions: PushNotificationPermissions) => void,
  ): void
  constructor(nativeNotif: Object)
  finish(fetchResult: string): void
  getAlert(): (null | Object | undefined) | (null | string | undefined)
  static getApplicationIconBadgeNumber(callback: Function): void
  static getAuthorizationStatus(
    callback: (authorizationStatus: number) => void,
  ): void
  getBadgeCount(): null | number | undefined
  getCategory(): null | string | undefined
  getContentAvailable(): ContentAvailable
  getData(): null | Object | undefined
  static getDeliveredNotifications(
    callback: (notifications: Array<Object>) => void,
  ): void
  static getInitialNotification(): Promise<null | PushNotification | undefined>
  getMessage(): (null | Object | undefined) | (null | string | undefined)
  static getScheduledLocalNotifications(callback: Function): void
  getSound(): null | string | undefined
  getThreadID(): null | string | undefined
  static presentLocalNotification(
    details: PresentLocalNotificationDetails,
  ): void
  static removeAllDeliveredNotifications(): void
  static removeDeliveredNotifications(identifiers: Array<string>): void
  static removeEventListener(type: PushNotificationEventName): void
  static requestPermissions(
    permissions?: PushNotificationPermissions,
  ): Promise<{
    alert: boolean
    badge: boolean
    sound: boolean
  }>
  static scheduleLocalNotification(
    details: ScheduleLocalNotificationDetails,
  ): void
  static setApplicationIconBadgeNumber(number: number): void
}
declare type PushNotificationPermissions = {
  [key: string]: boolean | number
  alert: boolean
  badge: boolean
  sound: boolean
}
declare type Rationale = {
  buttonNegative?: string
  buttonNeutral?: string
  buttonPositive?: string
  message: string
  title: string
}
declare type RCTDeviceEventDefinitions = {
  [name: string]: Array<any>
}
declare type RCTNetworkingEventDefinitions = {
  readonly didCompleteNetworkResponse: [[number, string, boolean]]
  readonly didReceiveNetworkData: [[number, string]]
  readonly didReceiveNetworkDataProgress: [[number, number, number]]
  readonly didReceiveNetworkIncrementalData: [[number, string, number, number]]
  readonly didReceiveNetworkResponse: [
    [
      number,
      number,
      (
        | undefined
        | {
            [$$Key$$: string]: string
          }
      ),
      string | undefined,
    ],
  ]
  readonly didSendNetworkData: [[number, number, number]]
}
declare class ReactNativeDocument_default extends ReadOnlyNode_default {
  get childElementCount(): number
  get children(): HTMLCollection_default<ReadOnlyElement_default>
  constructor(
    rootTag: RootTag,
    instanceHandle: ReactNativeDocumentInstanceHandle,
  )
  get documentElement(): ReactNativeElement_default
  get firstElementChild(): null | ReadOnlyElement_default
  getElementById(id: string): null | ReadOnlyElement_default
  get lastElementChild(): null | ReadOnlyElement_default
  get nodeName(): string
  get nodeType(): number
  get nodeValue(): null
  get textContent(): null
}
declare type ReactNativeDocumentElementInstanceHandle = symbol & {
  __ReactNativeDocumentElementInstanceHandle__: string
}
declare type ReactNativeDocumentInstanceHandle = symbol & {
  __ReactNativeDocumentInstanceHandle__: string
}
declare class ReactNativeElement_default
  extends ReadOnlyElement_default
  implements NativeMethods
{
  blur(): void
  constructor(
    tag: number,
    viewConfig: ViewConfig,
    instanceHandle: InstanceHandle,
    ownerDocument: ReactNativeDocument_default,
  )
  focus(): void
  measure(callback: MeasureOnSuccessCallback): void
  measureInWindow(callback: MeasureInWindowOnSuccessCallback): void
  measureLayout(
    relativeToNativeNode: HostInstance | number,
    onSuccess: MeasureLayoutOnSuccessCallback,
    onFail?: () => void,
  ): void
  get offsetHeight(): number
  get offsetLeft(): number
  get offsetParent(): null | ReadOnlyElement_default
  get offsetTop(): number
  get offsetWidth(): number
  setNativeProps(nativeProps: {}): void
}
declare class ReactNativeVersion {
  static major: number
  static minor: number
  static patch: number
  static prerelease: null | string
  static getVersionString(): string
}
declare class ReadOnlyCharacterData_default extends ReadOnlyNode_default {
  get data(): string
  get length(): number
  get nextElementSibling(): null | ReadOnlyElement_default
  get nodeValue(): string
  get previousElementSibling(): null | ReadOnlyElement_default
  substringData(offset: number, count: number): string
  get textContent(): string
}
declare class ReadOnlyElement_default extends ReadOnlyNode_default {
  get childElementCount(): number
  get children(): HTMLCollection_default<ReadOnlyElement_default>
  get clientHeight(): number
  get clientLeft(): number
  get clientTop(): number
  get clientWidth(): number
  get firstElementChild(): null | ReadOnlyElement_default
  getBoundingClientRect(): DOMRect_default
  hasPointerCapture(pointerId: number): boolean
  get id(): string
  get lastElementChild(): null | ReadOnlyElement_default
  get nextElementSibling(): null | ReadOnlyElement_default
  get nodeName(): string
  get nodeType(): number
  get nodeValue(): null | string
  set nodeValue(value: string)
  get previousElementSibling(): null | ReadOnlyElement_default
  releasePointerCapture(pointerId: number): void
  get scrollHeight(): number
  get scrollLeft(): number
  get scrollTop(): number
  get scrollWidth(): number
  setPointerCapture(pointerId: number): void
  get tagName(): string
  get textContent(): string
}
declare class ReadOnlyNode_default {
  static ATTRIBUTE_NODE: number
  static CDATA_SECTION_NODE: number
  static COMMENT_NODE: number
  static DOCUMENT_FRAGMENT_NODE: number
  static DOCUMENT_NODE: number
  static DOCUMENT_POSITION_CONTAINED_BY: number
  static DOCUMENT_POSITION_CONTAINS: number
  static DOCUMENT_POSITION_DISCONNECTED: number
  static DOCUMENT_POSITION_FOLLOWING: number
  static DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC: number
  static DOCUMENT_POSITION_PRECEDING: number
  static DOCUMENT_TYPE_NODE: number
  static ELEMENT_NODE: number
  static ENTITY_NODE: number
  static ENTITY_REFERENCE_NODE: number
  static NOTATION_NODE: number
  static PROCESSING_INSTRUCTION_NODE: number
  static TEXT_NODE: number
  get childNodes(): NodeList_default<ReadOnlyNode_default>
  compareDocumentPosition(otherNode: ReadOnlyNode_default): number
  constructor(
    instanceHandle: InstanceHandle,
    ownerDocument: null | ReactNativeDocument_default,
  )
  contains(otherNode: ReadOnlyNode_default): boolean
  get firstChild(): null | ReadOnlyNode_default
  getRootNode(): ReadOnlyNode_default
  hasChildNodes(): boolean
  get isConnected(): boolean
  get lastChild(): null | ReadOnlyNode_default
  get nextSibling(): null | ReadOnlyNode_default
  get nodeName(): string
  get nodeType(): number
  get nodeValue(): null | string
  get ownerDocument(): null | ReactNativeDocument_default
  get parentElement(): null | ReadOnlyElement_default
  get parentNode(): null | ReadOnlyNode_default
  get previousSibling(): null | ReadOnlyNode_default
  get textContent(): string
}
declare class ReadOnlyText_default extends ReadOnlyCharacterData_default {
  get nodeName(): string
  get nodeType(): number
}
declare type Rect = {
  readonly bottom?: number
  readonly left?: number
  readonly right?: number
  readonly top?: number
}
declare type RectOrSize = number | Rect
declare class RefreshControl extends React.Component<RefreshControlProps> {
  componentDidMount(): void
  componentDidUpdate(prevProps: RefreshControlProps): void
  render(): React.ReactNode
}
declare type RefreshControlBaseProps = {
  readonly onRefresh?: () => Promise<void> | void
  readonly progressViewOffset?: number
  readonly refreshing: boolean
}
declare type RefreshControlProps = Readonly<
  ViewProps &
    RefreshControlPropsIOS &
    RefreshControlPropsAndroid &
    RefreshControlBaseProps
>
declare type RefreshControlPropsAndroid = {
  readonly colors?: ReadonlyArray<ColorValue>
  readonly enabled?: boolean
  readonly progressBackgroundColor?: ColorValue
  readonly size?: "default" | "large"
}
declare type RefreshControlPropsIOS = {
  readonly tintColor?: ColorValue
  readonly title?: string
  readonly titleColor?: ColorValue
}
declare type registerCallableModule = typeof registerCallableModule
declare type RegisterCallableModule = (
  name: string,
  moduleOrFactory: (($$PARAM_0$$: void) => Module) | Module,
) => void
declare function registerCancellableHeadlessTask(
  taskKey: string,
  taskProvider: TaskProvider,
  taskCancelProvider: TaskCancelProvider,
): void
declare function registerComponent(
  appKey: string,
  componentProvider: ComponentProvider,
  section?: boolean,
): string
declare function registerConfig(config: Array<AppConfig>): void
declare function registerHeadlessTask(
  taskKey: string,
  taskProvider: TaskProvider,
): void
declare function registerRunnable(appKey: string, run: Runnable): string
declare function registerSection(
  appKey: string,
  component: ComponentProvider,
): void
declare type Registry = {
  runnables: Runnables
  sections: ReadonlyArray<string>
}
declare type RemotePressEvent = {
  readonly target: number
}
declare type RequestBody =
  | ArrayBuffer
  | ArrayBufferView
  | Blob_default
  | FormData_default
  | string
  | {
      uri: string
    }
declare type RequiredFlatListProps<ItemT> = {
  data: Readonly<ArrayLike<ItemT>> | undefined
}
declare type RequiredSectionListProps<ItemT, SectionT = DefaultSectionT> = {
  sections: ReadonlyArray<SectionListData<ItemT, SectionT>>
}
declare type RequiredVirtualizedListProps = {
  data?: any
  getItem: (data: any, index: number) => Item | undefined
  getItemCount: (data: any) => number
}
declare type RequiredVirtualizedSectionListProps<
  ItemT,
  SectionT = DefaultVirtualizedSectionT,
> = {
  sections: ReadonlyArray<SectionListData<ItemT, SectionT>>
}
declare type requireNativeComponent = typeof requireNativeComponent
declare type ResolvedAssetSource = {
  readonly height: number | undefined
  readonly scale: number
  readonly uri: string
  readonly width: number | undefined
}
declare type ResponderSyntheticEvent<T> = Readonly<
  NativeSyntheticEvent<T> & {
    touchHistory: {
      readonly indexOfSingleActiveTouch: number
      readonly mostRecentTimeStamp: number
      readonly numberActiveTouches: number
      readonly touchBank: ReadonlyArray<{
        readonly currentPageX: number
        readonly currentPageY: number
        readonly currentTimeStamp: number
        readonly previousPageX: number
        readonly previousPageY: number
        readonly previousTimeStamp: number
        readonly startPageX: number
        readonly startPageY: number
        readonly startTimeStamp: number
        readonly touchActive: boolean
      }>
    }
  }
>
declare type ReturnKeyType = "done" | "go" | "next" | "search" | "send"
declare type ReturnKeyTypeAndroid = "none" | "previous"
declare type ReturnKeyTypeIOS =
  | "default"
  | "emergency-call"
  | "google"
  | "join"
  | "route"
  | "yahoo"
declare type ReturnKeyTypeOptions =
  | ReturnKeyType
  | ReturnKeyTypeAndroid
  | ReturnKeyTypeIOS
declare type RgbaAnimatedValue = {
  readonly a: AnimatedValue_default
  readonly b: AnimatedValue_default
  readonly g: AnimatedValue_default
  readonly r: AnimatedValue_default
}
declare type RgbaValue = {
  readonly a: number
  readonly b: number
  readonly g: number
  readonly r: number
}
declare type Role =
  | "alert"
  | "alertdialog"
  | "application"
  | "article"
  | "banner"
  | "button"
  | "cell"
  | "checkbox"
  | "columnheader"
  | "combobox"
  | "complementary"
  | "contentinfo"
  | "definition"
  | "dialog"
  | "directory"
  | "document"
  | "feed"
  | "figure"
  | "form"
  | "grid"
  | "group"
  | "heading"
  | "img"
  | "link"
  | "list"
  | "listitem"
  | "log"
  | "main"
  | "marquee"
  | "math"
  | "menu"
  | "menubar"
  | "menuitem"
  | "meter"
  | "navigation"
  | "none"
  | "note"
  | "option"
  | "presentation"
  | "progressbar"
  | "radio"
  | "radiogroup"
  | "region"
  | "row"
  | "rowgroup"
  | "rowheader"
  | "scrollbar"
  | "searchbox"
  | "separator"
  | "slider"
  | "spinbutton"
  | "status"
  | "summary"
  | "switch"
  | "tab"
  | "table"
  | "tablist"
  | "tabpanel"
  | "term"
  | "timer"
  | "toolbar"
  | "tooltip"
  | "tree"
  | "treegrid"
  | "treeitem"
declare type RootTag = symbol & {
  __RootTag__: string
}
declare type RootTagContext = typeof RootTagContext
declare type RootViewStyleProvider = (appParameters: Object) => ViewStyleProp
declare function runApplication(
  appKey: string,
  appParameters: AppParameters,
  displayMode?: number,
): void
declare type Runnable = (
  appParameters: AppParameters,
  displayMode: DisplayModeType,
) => void
declare type Runnables = {
  [appKey: string]: Runnable
}
declare type SafeAreaView = typeof SafeAreaView
declare type ScaledSize = DisplayMetrics
declare type ScheduleLocalNotificationDetails =
  PresentLocalNotificationDetails & {
    repeatInterval?: "day" | "hour" | "minute" | "month" | "week" | "year"
  }
declare type ScrollEvent = NativeSyntheticEvent<NativeScrollEvent>
declare type ScrollResponderType = ScrollViewImperativeMethods
declare type ScrollToLocationParamsType = {
  animated?: boolean
  itemIndex: number
  sectionIndex: number
  viewOffset?: number
  viewPosition?: number
}
declare type ScrollView = typeof ScrollView
declare type ScrollViewBaseProps = {
  readonly children?: React.ReactNode
  readonly contentContainerStyle?: ViewStyleProp
  readonly contentOffset?: PointProp
  readonly decelerationRate?: DecelerationRateType
  readonly disableIntervalMomentum?: boolean
  readonly horizontal?: boolean
  readonly innerViewRef?: React.Ref<InnerViewInstance>
  readonly invertStickyHeaders?: boolean
  readonly keyboardDismissMode?: "interactive" | "none" | "on-drag"
  readonly keyboardShouldPersistTaps?:
    | "always"
    | "handled"
    | "never"
    | false
    | true
  readonly maintainVisibleContentPosition?: {
    readonly autoscrollToTopThreshold?: number
    readonly minIndexForVisible: number
  }
  readonly onMomentumScrollBegin?: (event: ScrollEvent) => void
  readonly onMomentumScrollEnd?: (event: ScrollEvent) => void
  readonly onScroll?: (event: ScrollEvent) => void
  readonly onScrollBeginDrag?: (event: ScrollEvent) => void
  readonly onScrollEndDrag?: (event: ScrollEvent) => void
  readonly pagingEnabled?: boolean
  readonly refreshControl?: React.JSX.Element
  readonly removeClippedSubviews?: boolean
  readonly scrollEnabled?: boolean
  readonly scrollEventThrottle?: number
  readonly scrollViewRef?: React.Ref<PublicScrollViewInstance>
  readonly showsVerticalScrollIndicator?: boolean
  readonly snapToAlignment?: "center" | "end" | "start"
  readonly snapToEnd?: boolean
  readonly snapToInterval?: number
  readonly snapToOffsets?: ReadonlyArray<number>
  readonly snapToStart?: boolean
  readonly StickyHeaderComponent?: StickyHeaderComponentType
  readonly stickyHeaderHiddenOnScroll?: boolean
  readonly stickyHeaderIndices?: ReadonlyArray<number>
  readonly onContentSizeChange?: (
    contentWidth: number,
    contentHeight: number,
  ) => void
  readonly onKeyboardDidHide?: (event: KeyboardEvent) => void
  readonly onKeyboardDidShow?: (event: KeyboardEvent) => void
  readonly onKeyboardWillHide?: (event: KeyboardEvent) => void
  readonly onKeyboardWillShow?: (event: KeyboardEvent) => void
}
declare type ScrollViewComponentStatics = {
  readonly Context: typeof $$ScrollViewContext
}
declare interface ScrollViewImperativeMethods {
  readonly flashScrollIndicators: () => void
  readonly getInnerViewNode: () => number | undefined
  readonly getInnerViewRef: () => InnerViewInstance | null
  readonly getNativeScrollRef: () => HostInstance | null
  readonly getScrollableNode: () => number | undefined
  readonly getScrollResponder: () => ScrollResponderType
  readonly scrollResponderScrollNativeHandleToKeyboard: (
    nodeHandle: HostInstance | number,
    additionalOffset?: number,
    preventNegativeScrollOffset?: boolean,
  ) => void
  readonly scrollResponderZoomTo: (
    rect: {
      animated?: boolean
      height: number
      width: number
      x: number
      y: number
    },
    animated?: boolean,
  ) => void
  readonly scrollTo: (
    options?: number | ScrollViewScrollToOptions,
    deprecatedX?: number,
    deprecatedAnimated?: boolean,
  ) => void
  readonly scrollToEnd: (
    options?: ScrollViewScrollToOptions | undefined,
  ) => void
}
declare type ScrollViewNativeComponent = typeof $$ScrollViewNativeComponent
declare type ScrollViewNativeProps = Readonly<
  Omit<ViewProps, "onResponderGrant"> & {
    alwaysBounceHorizontal?: boolean
    alwaysBounceVertical?: boolean
    automaticallyAdjustContentInsets?: boolean
    automaticallyAdjustKeyboardInsets?: boolean
    automaticallyAdjustsScrollIndicatorInsets?: boolean
    bounces?: boolean
    bouncesZoom?: boolean
    canCancelContentTouches?: boolean
    centerContent?: boolean
    contentInset?: EdgeInsetsProp
    contentInsetAdjustmentBehavior?:
      | "always"
      | "automatic"
      | "never"
      | "scrollableAxes"
    contentOffset?: PointProp
    decelerationRate?: "fast" | "normal" | number
    directionalLockEnabled?: boolean
    disableIntervalMomentum?: boolean
    endFillColor?: ColorValue
    fadingEdgeLength?:
      | (number | undefined)
      | {
          end: number
          start: number
        }
    indicatorStyle?: "black" | "default" | "white"
    isInvertedVirtualizedList?: boolean
    keyboardDismissMode?: "interactive" | "none" | "on-drag"
    maintainVisibleContentPosition?: {
      readonly autoscrollToTopThreshold?: number
      readonly minIndexForVisible: number
    }
    maximumZoomScale?: number
    minimumZoomScale?: number
    nestedScrollEnabled?: boolean
    onMomentumScrollBegin?: (event: ScrollEvent) => void
    onMomentumScrollEnd?: (event: ScrollEvent) => void
    onResponderGrant?: (e: GestureResponderEvent) => boolean | void
    onScroll?: (event: ScrollEvent) => void
    onScrollBeginDrag?: (event: ScrollEvent) => void
    onScrollEndDrag?: (event: ScrollEvent) => void
    overScrollMode?: "always" | "auto" | "never"
    pagingEnabled?: boolean
    persistentScrollbar?: boolean
    pinchGestureEnabled?: boolean
    scrollEnabled?: boolean
    scrollEventThrottle?: number
    scrollIndicatorInsets?: EdgeInsetsProp
    scrollPerfTag?: string
    scrollsToTop?: boolean
    scrollToOverflowEnabled?: boolean
    sendMomentumEvents?: boolean
    showsHorizontalScrollIndicator?: boolean
    showsScrollIndex?: boolean
    showsVerticalScrollIndicator?: boolean
    snapToAlignment?: "center" | "end" | "start"
    snapToEnd?: boolean
    snapToInterval?: number
    snapToOffsets?: ReadonlyArray<number>
    snapToStart?: boolean
    zoomScale?: number
    onScrollToTop?: (event: ScrollEvent) => void
  }
>
declare type ScrollViewProps = Readonly<
  ViewProps & ScrollViewPropsIOS & ScrollViewPropsAndroid & ScrollViewBaseProps
>
declare type ScrollViewPropsAndroid = {
  readonly endFillColor?: ColorValue
  readonly fadingEdgeLength?:
    | (number | undefined)
    | {
        end: number
        start: number
      }
  readonly nestedScrollEnabled?: boolean
  readonly overScrollMode?: "always" | "auto" | "never"
  readonly persistentScrollbar?: boolean
  readonly scrollPerfTag?: string
}
declare type ScrollViewPropsIOS = {
  readonly alwaysBounceHorizontal?: boolean
  readonly alwaysBounceVertical?: boolean
  readonly automaticallyAdjustContentInsets?: boolean
  readonly automaticallyAdjustKeyboardInsets?: boolean
  readonly automaticallyAdjustsScrollIndicatorInsets?: boolean
  readonly bounces?: boolean
  readonly bouncesZoom?: boolean
  readonly canCancelContentTouches?: boolean
  readonly centerContent?: boolean
  readonly contentInset?: EdgeInsetsProp
  readonly contentInsetAdjustmentBehavior?:
    | "always"
    | "automatic"
    | "never"
    | "scrollableAxes"
  readonly directionalLockEnabled?: boolean
  readonly disableScrollViewPanResponder?: boolean
  readonly indicatorStyle?: "black" | "default" | "white"
  readonly maximumZoomScale?: number
  readonly minimumZoomScale?: number
  readonly pinchGestureEnabled?: boolean
  readonly scrollIndicatorInsets?: EdgeInsetsProp
  readonly scrollsToTop?: boolean
  readonly scrollToOverflowEnabled?: boolean
  readonly showsHorizontalScrollIndicator?: boolean
  readonly showsScrollIndex?: boolean
  readonly zoomScale?: number
  readonly onScrollToTop?: (event: ScrollEvent) => void
}
declare interface ScrollViewScrollToOptions {
  animated?: boolean
  x?: number
  y?: number
}
declare type ScrollViewStickyHeaderProps = {
  readonly children?: React.ReactNode
  readonly hiddenOnScroll?: boolean
  readonly inverted: boolean | undefined
  readonly nativeID?: string
  readonly nextHeaderLayoutY: number | undefined
  readonly scrollAnimatedValue: Animated.Value
  readonly scrollViewHeight: number | undefined
  readonly onLayout: (event: LayoutChangeEvent) => void
}
declare type SectionBase<
  SectionItemT,
  SectionT = DefaultVirtualizedSectionT,
> = {
  data: ReadonlyArray<SectionItemT>
  ItemSeparatorComponent?: React.ComponentType<any> | React.JSX.Element
  key?: string
  renderItem?: (info: {
    index: number
    item: SectionItemT
    section: SectionListData<SectionItemT, SectionT>
    separators: {
      highlight: () => void
      unhighlight: () => void
      updateProps: (select: "leading" | "trailing", newProps: Object) => void
    }
  }) => null | React.JSX.Element
  keyExtractor?: (
    item: SectionItemT | undefined,
    index?: number | undefined,
  ) => string
}
declare class SectionList<
  ItemT = any,
  SectionT = DefaultSectionT,
> extends React.PureComponent<SectionListProps<ItemT, SectionT>> {
  props: SectionListProps<ItemT, SectionT>
  flashScrollIndicators(): void
  getScrollableNode(): any
  getScrollResponder(): null | ScrollResponderType | undefined
  recordInteraction(): void
  render(): React.ReactNode
  scrollToLocation(params: ScrollToLocationParamsType): void
  setNativeProps(props: Object): void
}
declare type SectionListData<
  SectionItemT,
  SectionT = DefaultVirtualizedSectionT,
> =
  | (Readonly<SectionBase<SectionItemT, SectionT>> & SectionT)
  | (SectionBase<SectionItemT, SectionT> & SectionT)
  | SectionT
declare type SectionListProps<ItemT, SectionT = DefaultSectionT> = Omit<
  Omit<
    VirtualizedSectionListProps<ItemT, SectionT>,
    "getItem" | "getItemCount" | "keyExtractor" | "renderItem"
  >,
  | "extraData"
  | "initialNumToRender"
  | "inverted"
  | "keyExtractor"
  | "onEndReached"
  | "removeClippedSubviews"
  | "renderItem"
  | "sections"
  | never
> &
  RequiredSectionListProps<ItemT, SectionT> &
  OptionalSectionListProps<ItemT, SectionT>
declare type SectionListRenderItem<ItemT, SectionT = DefaultSectionT> = (
  info: SectionListRenderItemInfo<ItemT, SectionT>,
) => null | React.ReactNode
declare type SectionListRenderItemInfo<
  ItemT,
  SectionT = DefaultSectionT,
> = ListRenderItemInfo<ItemT> & {
  section: SectionListData<ItemT, SectionT>
}
declare type Selection = {
  readonly end: number
  readonly start: number
}
declare type Separators = {
  highlight: () => void
  unhighlight: () => void
  updateProps: (select: "leading" | "trailing", newProps: Object) => void
}
declare type sequence = typeof sequence
declare function setColorScheme(colorScheme: ColorSchemeName): void
declare function setComponentProviderInstrumentationHook(
  hook: ComponentProviderInstrumentationHook,
): void
declare function setEnabled(_doEnable: boolean): void
declare function setLayoutAnimationEnabled(value: boolean): void
declare function setRootViewStyleProvider(provider: RootViewStyleProvider): void
declare function setRuntimeConfigProvider(
  runtimeConfigProvider: (name: string) =>
    | null
    | undefined
    | {
        native: boolean
        verify: boolean
      },
): void
declare type setStyleAttributePreprocessor =
  typeof setStyleAttributePreprocessor
declare function setSurfaceProps(
  appKey: string,
  appParameters: Object,
  displayMode?: number,
): void
declare type Settings = typeof Settings
declare function setWrapperComponentProvider(
  provider: WrapperComponentProvider,
): void
declare class Share {
  static dismissedAction: "dismissedAction"
  static sharedAction: "sharedAction"
  static share(
    content: ShareContent,
    options?: ShareOptions,
  ): Promise<{
    action: string
    activityType: string | undefined
  }>
}
declare type ShareAction = {
  action: "dismissedAction" | "sharedAction"
  activityType?: null | string
}
declare type ShareActionSheetError = {
  readonly code: string
  readonly domain: string
  readonly message: string
  readonly userInfo?: Object
}
declare type ShareActionSheetIOSOptions = {
  readonly anchor?: number
  readonly cancelButtonTintColor?: number
  readonly disabledButtonTintColor?: number
  readonly excludedActivityTypes?: Array<string>
  readonly message?: string
  readonly subject?: string
  readonly tintColor?: number
  readonly url?: string
  readonly userInterfaceStyle?: string
}
declare type ShareContent =
  | {
      message: string
      title?: string
      url?: string
    }
  | {
      message?: string
      title?: string
      url: string
    }
declare type ShareOptions = {
  anchor?: number
  dialogTitle?: string
  excludedActivityTypes?: Array<string>
  subject?: string
  tintColor?: ColorValue
}
declare type SimpleTask = {
  name: string
  run: () => void
}
declare interface Spec extends TurboModule {
  readonly getConstants: () => {
    readonly buttonClicked: DialogAction
    readonly buttonNegative: DialogButtonKey
    readonly buttonNeutral: DialogButtonKey
    readonly buttonPositive: DialogButtonKey
    readonly dismissed: DialogAction
  }
  readonly showAlert: (
    config: DialogOptions,
    onError: (error: string) => void,
    onAction: (action: DialogAction, buttonKey?: DialogButtonKey) => void,
  ) => void
}
declare interface Spec_2 extends TurboModule {
  readonly blur?: (reactTag: number) => void
  readonly clearJSResponder: () => void
  readonly configureNextLayoutAnimation: (
    config: Object,
    callback: () => void,
    errorCallback: (error: Object) => void,
  ) => void
  readonly createView: (
    reactTag: number,
    viewName: string,
    rootTag: RootTag,
    props: Object,
  ) => void
  readonly dispatchViewManagerCommand: (
    reactTag: number,
    commandID: number,
    commandArgs?: Array<any>,
  ) => void
  readonly findSubviewIn: (
    reactTag: number,
    point: Array<number>,
    callback: (
      nativeViewTag: number,
      left: number,
      top: number,
      width: number,
      height: number,
    ) => void,
  ) => void
  readonly focus?: (reactTag: number) => void
  readonly getConstants: () => Object
  readonly getConstantsForViewManager?: (
    viewManagerName: string,
  ) => Object | undefined
  readonly getDefaultEventTypes?: () => Array<string>
  readonly lazilyLoadView?: (name: string) => Object
  readonly manageChildren: (
    containerTag: number,
    moveFromIndices: Array<number>,
    moveToIndices: Array<number>,
    addChildReactTags: Array<number>,
    addAtIndices: Array<number>,
    removeAtIndices: Array<number>,
  ) => void
  readonly measure: (
    reactTag: number,
    callback: NativeMeasureOnSuccessCallback,
  ) => void
  readonly measureInWindow: (
    reactTag: number,
    callback: NativeMeasureInWindowOnSuccessCallback,
  ) => void
  readonly measureLayout: (
    reactTag: number,
    ancestorReactTag: number,
    errorCallback: (error: Object) => void,
    callback: NativeMeasureLayoutOnSuccessCallback,
  ) => void
  readonly measureLayoutRelativeToParent: (
    reactTag: number,
    errorCallback: (error: Object) => void,
    callback: (
      left: number,
      top: number,
      width: number,
      height: number,
    ) => void,
  ) => void
  readonly sendAccessibilityEvent?: (
    reactTag: number,
    eventType: number,
  ) => void
  readonly setChildren: (containerTag: number, reactTags: Array<number>) => void
  readonly setJSResponder: (
    reactTag: number,
    blockNativeResponder: boolean,
  ) => void
  readonly setLayoutAnimationEnabledExperimental?: (enabled: boolean) => void
  readonly updateView: (
    reactTag: number,
    viewName: string,
    props: Object,
  ) => void
  readonly viewIsDescendantOf: (
    reactTag: number,
    ancestorReactTag: number,
    callback: (result: Array<boolean>) => void,
  ) => void
}
declare type spring = typeof spring
declare type SpringAnimationConfig = Readonly<
  AnimationConfig & {
    bounciness?: number
    damping?: number
    delay?: number
    friction?: number
    mass?: number
    overshootClamping?: boolean
    restDisplacementThreshold?: number
    restSpeedThreshold?: number
    speed?: number
    stiffness?: number
    tension?: number
    toValue:
      | AnimatedColor_default
      | AnimatedInterpolation_default<number>
      | AnimatedValue_default
      | AnimatedValueXY_default
      | number
      | {
          a: number
          b: number
          g: number
          r: number
        }
      | {
          x: number
          y: number
        }
    velocity?:
      | number
      | {
          readonly x: number
          readonly y: number
        }
  }
>
declare type StackFrame = {
  collapse?: boolean
  column: number | undefined
  file: string | undefined
  lineNumber: number | undefined
  methodName: string
}
declare type StackProps = {
  backgroundColor:
    | undefined
    | {
        animated: boolean
        value: StatusBarProps["backgroundColor"]
      }
  barStyle:
    | undefined
    | {
        animated: boolean
        value: StatusBarProps["barStyle"]
      }
  hidden:
    | undefined
    | {
        animated: boolean
        transition: StatusBarProps["showHideTransition"]
        value: boolean
      }
  networkActivityIndicatorVisible: StatusBarProps["networkActivityIndicatorVisible"]
  translucent: StatusBarProps["translucent"]
}
declare type stagger = typeof stagger
declare function startHeadlessTask(
  taskId: number,
  taskKey: string,
  data: any,
): void
declare type State = {
  cellsAroundViewport: {
    first: number
    last: number
  }
  firstVisibleItemKey: string | undefined
  pendingScrollUpdateCount: number
  renderMask: CellRenderMask
}
declare class StateSafePureComponent_default<
  Props,
  State extends {},
> extends React.PureComponent<Props, State> {
  constructor(props: Props)
  setState<K extends keyof State>(
    partialState:
      | (
          | ((
              $$PARAM_0$$: State,
              $$PARAM_1$$: Props,
            ) => null | Pick<State, K> | undefined)
          | Pick<State, K>
        )
      | null
      | undefined,
    callback?: () => unknown,
  ): void
}
declare class StatusBar extends React.Component<StatusBarProps> {
  static currentHeight: null | number | undefined
  componentDidMount(): void
  componentDidUpdate(): void
  componentWillUnmount(): void
  static popStackEntry(entry: StackProps): void
  static pushStackEntry(props: StatusBarProps): StackProps
  render(): React.ReactNode
  static replaceStackEntry(entry: StackProps, props: StatusBarProps): StackProps
  static setBackgroundColor(color: ColorValue, animated?: boolean): void
  static setBarStyle(style: StatusBarStyle, animated?: boolean): void
  static setHidden(hidden: boolean, animation?: StatusBarAnimation): void
  static setNetworkActivityIndicatorVisible(visible: boolean): void
  static setTranslucent(translucent: boolean): void
}
declare type StatusBarAnimation = keyof {
  fade: string
  none: string
  slide: string
}
declare type StatusBarBaseProps = {
  readonly animated?: boolean
  readonly barStyle?: "dark-content" | "default" | "light-content"
  readonly hidden?: boolean
}
declare type StatusBarProps = Readonly<
  StatusBarPropsAndroid & StatusBarPropsIOS & StatusBarBaseProps
>
declare type StatusBarPropsAndroid = {
  readonly backgroundColor?: ColorValue
  readonly translucent?: boolean
}
declare type StatusBarPropsIOS = {
  readonly networkActivityIndicatorVisible?: boolean
  readonly showHideTransition?: "fade" | "none" | "slide"
}
declare type StatusBarStyle = keyof {
  "dark-content": string
  default: string
  "light-content": string
}
declare type StickyHeaderComponentType = (
  props: ScrollViewStickyHeaderProps & {
    ref?: React.Ref<{
      readonly setNextHeaderY: ($$PARAM_0$$: number) => void
    }>
  },
) => React.ReactNode
declare type StyleProp<T> =
  | ""
  | false
  | null
  | ReadonlyArray<StyleProp<T>>
  | T
  | void
declare namespace StyleSheet {
  export {
    AbsoluteFillStyle,
    hairlineWidth,
    absoluteFill,
    absoluteFillObject,
    compose,
    flatten,
    setStyleAttributePreprocessor,
    create,
  }
}
declare type SubmitBehavior = "blurAndSubmit" | "newline" | "submit"
declare type subtract = typeof subtract
declare type Switch = typeof Switch
declare type SwitchChangeEvent = NativeSyntheticEvent<SwitchChangeEventData>
declare type SwitchChangeEventData = {
  readonly target: number
  readonly value: boolean
}
declare type SwitchNativeProps = Readonly<
  ViewProps & {
    disabled?: WithDefault<boolean, false>
    onChange?: BubblingEventHandler<NativeSwitchChangeEvent>
    onTintColor?: ColorValue
    thumbColor?: ColorValue
    thumbTintColor?: ColorValue
    tintColor?: ColorValue
    trackColorForFalse?: ColorValue
    trackColorForTrue?: ColorValue
    value?: WithDefault<boolean, false>
  }
>
declare type SwitchProps = Readonly<
  ViewProps & SwitchPropsIOS & SwitchPropsBase
>
declare type SwitchPropsBase = {
  disabled?: boolean
  ios_backgroundColor?: ColorValue
  onChange?: (event: SwitchChangeEvent) => Promise<void> | void
  onValueChange?: (value: boolean) => Promise<void> | void
  thumbColor?: ColorValue
  trackColor?: {
    readonly false?: ColorValue
    readonly true?: ColorValue
  }
  value?: boolean
}
declare type SwitchPropsIOS = {
  onTintColor?: ColorValue
  thumbTintColor?: ColorValue
  tintColor?: ColorValue
}
declare type SwitchRef = React.ComponentRef<
  typeof $$AndroidSwitchNativeComponent | typeof $$SwitchNativeComponent
>
declare namespace Systrace {
  export {
    isEnabled,
    setEnabled,
    beginEvent,
    endEvent,
    beginAsyncEvent,
    endAsyncEvent,
    counterEvent,
    EventName,
    EventArgs,
  }
}
declare type TargetedEvent = {
  readonly target: number
}
declare type TargetEvent = {
  readonly target: number
}
declare type Task = (() => void) | PromiseTask | SimpleTask
declare type TaskCanceller = () => void
declare type TaskCancelProvider = () => TaskCanceller
declare type TaskProvider = () => HeadlessTask
declare type TBackHandler = {
  addEventListener(
    eventName: BackPressEventName,
    handler: () => boolean | undefined,
  ): {
    remove: () => void
  }
  exitApp(): void
}
declare type Text = typeof Text
declare type TextBaseProps = {
  readonly allowFontScaling?: boolean
  readonly android_hyphenationFrequency?: "full" | "none" | "normal"
  readonly children?: React.ReactNode
  readonly ellipsizeMode?: "clip" | "head" | "middle" | "tail"
  readonly id?: string
  readonly maxFontSizeMultiplier?: number
  readonly nativeID?: string
  readonly numberOfLines?: number
  readonly onAccessibilityAction?: (event: AccessibilityActionEvent) => unknown
  readonly onLayout?: (event: LayoutChangeEvent) => unknown
  readonly onLongPress?: (event: GestureResponderEvent) => unknown
  readonly onMoveShouldSetResponder?: () => boolean
  readonly onPress?: (event: GestureResponderEvent) => unknown
  readonly onPressIn?: (event: GestureResponderEvent) => unknown
  readonly onPressOut?: (event: GestureResponderEvent) => unknown
  readonly onResponderGrant?: (event: GestureResponderEvent) => void
  readonly onResponderMove?: (event: GestureResponderEvent) => void
  readonly onResponderRelease?: (event: GestureResponderEvent) => void
  readonly onResponderTerminate?: (event: GestureResponderEvent) => void
  readonly onResponderTerminationRequest?: () => boolean
  readonly onStartShouldSetResponder?: () => boolean
  readonly onTextLayout?: (event: TextLayoutEvent) => unknown
  readonly pressRetentionOffset?: PressRetentionOffset
  readonly role?: Role
  readonly selectable?: boolean
  readonly style?: TextStyleProp
  readonly testID?: string
}
declare type TextContentType =
  | "addressCity"
  | "addressCityAndState"
  | "addressState"
  | "birthdate"
  | "birthdateDay"
  | "birthdateMonth"
  | "birthdateYear"
  | "cellularEID"
  | "cellularIMEI"
  | "countryName"
  | "creditCardExpiration"
  | "creditCardExpirationMonth"
  | "creditCardExpirationYear"
  | "creditCardFamilyName"
  | "creditCardGivenName"
  | "creditCardMiddleName"
  | "creditCardName"
  | "creditCardNumber"
  | "creditCardSecurityCode"
  | "creditCardType"
  | "dateTime"
  | "emailAddress"
  | "familyName"
  | "flightNumber"
  | "fullStreetAddress"
  | "givenName"
  | "jobTitle"
  | "location"
  | "middleName"
  | "name"
  | "namePrefix"
  | "nameSuffix"
  | "newPassword"
  | "nickname"
  | "none"
  | "oneTimeCode"
  | "organizationName"
  | "password"
  | "postalCode"
  | "shipmentTrackingNumber"
  | "streetAddressLine1"
  | "streetAddressLine2"
  | "sublocality"
  | "telephoneNumber"
  | "URL"
  | "username"
declare type TextForwardRef = React.ComponentRef<
  typeof NativeText | typeof NativeVirtualText
>
declare type TextInput = typeof TextInput
declare type TextInputAndroidProps = {
  readonly cursorColor?: ColorValue
  readonly disableFullscreenUI?: boolean
  readonly importantForAutofill?:
    | "auto"
    | "no"
    | "noExcludeDescendants"
    | "yes"
    | "yesExcludeDescendants"
  readonly inlineImageLeft?: string
  readonly inlineImagePadding?: number
  readonly numberOfLines?: number
  readonly returnKeyLabel?: string
  readonly rows?: number
  readonly selectionHandleColor?: ColorValue
  readonly showSoftInputOnFocus?: boolean
  readonly textBreakStrategy?: "balanced" | "highQuality" | "simple"
  readonly underlineColorAndroid?: ColorValue
}
declare type TextInputBaseProps = {
  readonly allowFontScaling?: boolean
  readonly autoCapitalize?: AutoCapitalize
  readonly autoComplete?:
    | "additional-name"
    | "address-line1"
    | "address-line2"
    | "birthdate-day"
    | "birthdate-full"
    | "birthdate-month"
    | "birthdate-year"
    | "cc-csc"
    | "cc-exp-day"
    | "cc-exp-month"
    | "cc-exp-year"
    | "cc-exp"
    | "cc-family-name"
    | "cc-given-name"
    | "cc-middle-name"
    | "cc-name"
    | "cc-number"
    | "cc-type"
    | "country"
    | "current-password"
    | "email"
    | "family-name"
    | "gender"
    | "given-name"
    | "honorific-prefix"
    | "honorific-suffix"
    | "name-family"
    | "name-given"
    | "name-middle-initial"
    | "name-middle"
    | "name-prefix"
    | "name-suffix"
    | "name"
    | "new-password"
    | "nickname"
    | "off"
    | "one-time-code"
    | "organization-title"
    | "organization"
    | "password-new"
    | "password"
    | "postal-address-country"
    | "postal-address-extended-postal-code"
    | "postal-address-extended"
    | "postal-address-locality"
    | "postal-address-region"
    | "postal-address"
    | "postal-code"
    | "sms-otp"
    | "street-address"
    | "tel-country-code"
    | "tel-device"
    | "tel-national"
    | "tel"
    | "url"
    | "username-new"
    | "username"
  readonly autoCorrect?: boolean
  readonly autoFocus?: boolean
  readonly blurOnSubmit?: boolean
  readonly caretHidden?: boolean
  readonly contextMenuHidden?: boolean
  readonly defaultValue?: string
  readonly editable?: boolean
  readonly enterKeyHint?: EnterKeyHintTypeOptions
  readonly forwardedRef?: React.Ref<TextInputInstance>
  readonly inputMode?: InputModeOptions
  readonly keyboardType?: KeyboardTypeOptions
  readonly maxFontSizeMultiplier?: number
  readonly maxLength?: number
  readonly multiline?: boolean
  readonly onBlur?: (e: TextInputBlurEvent) => unknown
  readonly onChange?: (e: TextInputChangeEvent) => unknown
  readonly onChangeText?: (text: string) => unknown
  readonly onContentSizeChange?: (e: TextInputContentSizeChangeEvent) => unknown
  readonly onEndEditing?: (e: TextInputEndEditingEvent) => unknown
  readonly onFocus?: (e: TextInputFocusEvent) => unknown
  readonly onKeyPress?: (e: TextInputKeyPressEvent) => unknown
  readonly onPress?: (event: GestureResponderEvent) => unknown
  readonly onPressIn?: (event: GestureResponderEvent) => unknown
  readonly onPressOut?: (event: GestureResponderEvent) => unknown
  readonly onScroll?: (e: ScrollEvent) => unknown
  readonly onSelectionChange?: (e: TextInputSelectionChangeEvent) => unknown
  readonly onSubmitEditing?: (e: TextInputSubmitEditingEvent) => unknown
  readonly placeholder?: string
  readonly placeholderTextColor?: ColorValue
  readonly readOnly?: boolean
  readonly returnKeyType?: ReturnKeyTypeOptions
  readonly secureTextEntry?: boolean
  readonly selection?: {
    readonly end?: number
    readonly start: number
  }
  readonly selectionColor?: ColorValue
  readonly selectTextOnFocus?: boolean
  readonly style?: TextStyleProp
  readonly submitBehavior?: SubmitBehavior
  readonly textAlign?: "center" | "left" | "right"
  readonly value?: string
}
declare type TextInputBlurEvent = BlurEvent
declare type TextInputChangeEvent =
  NativeSyntheticEvent<TextInputChangeEventData>
declare type TextInputChangeEventData = {
  readonly eventCount: number
  readonly target: number
  readonly text: string
}
declare type TextInputComponentStatics = {
  readonly State: {
    readonly blurTextInput: (textField: HostInstance | undefined) => void
    readonly currentlyFocusedField: () => number | undefined
    readonly currentlyFocusedInput: () => HostInstance | undefined
    readonly focusTextInput: (textField: HostInstance | undefined) => void
  }
}
declare type TextInputContentSizeChangeEvent =
  NativeSyntheticEvent<TextInputContentSizeChangeEventData>
declare type TextInputContentSizeChangeEventData = {
  readonly contentSize: {
    readonly height: number
    readonly width: number
  }
  readonly target: number
}
declare type TextInputEndEditingEvent =
  NativeSyntheticEvent<TextInputEndEditingEventData>
declare type TextInputEndEditingEventData = Readonly<
  TargetEvent & {
    eventCount: number
    text: string
  }
>
declare type TextInputFocusEvent = FocusEvent
declare type TextInputInstance = _TextInputInstance
declare type TextInputIOSProps = {
  readonly clearButtonMode?:
    | "always"
    | "never"
    | "unless-editing"
    | "while-editing"
  readonly clearTextOnFocus?: boolean
  readonly dataDetectorTypes?:
    | (DataDetectorTypesType | undefined)
    | ReadonlyArray<DataDetectorTypesType>
  readonly disableKeyboardShortcuts?: boolean
  readonly enablesReturnKeyAutomatically?: boolean
  readonly inputAccessoryViewButtonLabel?: string
  readonly inputAccessoryViewID?: string
  readonly keyboardAppearance?: "dark" | "default" | "light"
  readonly lineBreakModeIOS?:
    | "char"
    | "clip"
    | "head"
    | "middle"
    | "tail"
    | "wordWrapping"
  readonly lineBreakStrategyIOS?:
    | "hangul-word"
    | "none"
    | "push-out"
    | "standard"
  readonly passwordRules?: PasswordRules
  readonly rejectResponderTermination?: boolean
  readonly scrollEnabled?: boolean
  readonly smartInsertDelete?: boolean
  readonly spellCheck?: boolean
  readonly textContentType?: TextContentType
}
declare type TextInputKeyPressEvent =
  NativeSyntheticEvent<TextInputKeyPressEventData>
declare type TextInputKeyPressEventData = Readonly<
  Omit<TargetEvent, "target"> & {
    eventCount: number
    key: string
    target?: number
  }
>
declare type TextInputProps = Readonly<
  Omit<ViewProps, "style"> &
    TextInputIOSProps &
    TextInputAndroidProps &
    TextInputBaseProps
>
declare type TextInputSelectionChangeEvent =
  NativeSyntheticEvent<TextInputSelectionChangeEventData>
declare type TextInputSelectionChangeEventData = Readonly<
  TargetEvent & {
    selection: Selection
  }
>
declare type TextInputSubmitEditingEvent =
  NativeSyntheticEvent<TextInputSubmitEditingEventData>
declare type TextInputSubmitEditingEventData = Readonly<
  TargetEvent & {
    eventCount: number
    text: string
  }
>
declare type TextInputType = InternalTextInput & TextInputComponentStatics
declare type TextLayoutEvent = NativeSyntheticEvent<TextLayoutEventData>
declare type TextLayoutEventData = {
  readonly lines: Array<TextLayoutLine>
}
declare type TextLayoutLine = Readonly<
  LayoutRectangle & {
    ascender: number
    capHeight: number
    descender: number
    text: string
    xHeight: number
  }
>
declare type TextPointerEventProps = {
  readonly onPointerEnter?: (event: PointerEvent) => void
  readonly onPointerLeave?: (event: PointerEvent) => void
  readonly onPointerMove?: (event: PointerEvent) => void
}
declare type TextProps = Readonly<
  TextPointerEventProps &
    TextPropsIOS &
    TextPropsAndroid &
    TextBaseProps &
    AccessibilityProps
>
declare type TextPropsAndroid = {
  adjustsFontSizeToFit?: boolean
  dataDetectorType?: "all" | "email" | "link" | "none" | "phoneNumber"
  disabled?: boolean
  minimumFontScale?: number
  selectionColor?: ColorValue
  textBreakStrategy?: "balanced" | "highQuality" | "simple"
}
declare type TextPropsIOS = {
  adjustsFontSizeToFit?: boolean
  dynamicTypeRamp?:
    | "body"
    | "callout"
    | "caption1"
    | "caption2"
    | "footnote"
    | "headline"
    | "largeTitle"
    | "subheadline"
    | "title1"
    | "title2"
    | "title3"
  lineBreakStrategyIOS?: "hangul-word" | "none" | "push-out" | "standard"
  suppressHighlighting?: boolean
}
declare type TextStyle = ____TextStyle_Internal
declare type TextStyleProp = ____TextStyleProp_Internal
declare type Timespan = {
  endExtras?: Extras
  endTime?: number
  startExtras?: Extras
  startTime: number
  totalTime?: number
}
declare type timing = typeof timing
declare type TimingAnimationConfig = Readonly<
  AnimationConfig & {
    delay?: number
    duration?: number
    toValue:
      | AnimatedColor_default
      | AnimatedInterpolation_default<number>
      | AnimatedValue_default
      | AnimatedValueXY_default
      | number
      | RgbaValue
      | {
          readonly x: number
          readonly y: number
        }
    easing?: (value: number) => number
  }
>
declare type ToastAndroid = typeof ToastAndroid
declare type Touchable = typeof Touchable
declare type TouchableHighlight = typeof TouchableHighlight
declare type TouchableHighlightBaseProps = {
  readonly activeOpacity?: number
  readonly hostRef?: React.Ref<React.ComponentRef<typeof View>>
  readonly onHideUnderlay?: () => void
  readonly onShowUnderlay?: () => void
  readonly style?: ViewStyleProp
  readonly testOnly_pressed?: boolean
  readonly underlayColor?: ColorValue
}
declare type TouchableHighlightProps = Readonly<
  TouchableWithoutFeedbackProps & TVViewProps & TouchableHighlightBaseProps
>
declare class TouchableNativeFeedback extends React.Component<
  TouchableNativeFeedbackProps,
  TouchableNativeFeedbackState
> {
  state: TouchableNativeFeedbackState
  static canUseNativeForeground: () => boolean
  static Ripple: (
    color: string,
    borderless: boolean,
    rippleRadius?: null | number | undefined,
  ) => {
    readonly borderless: boolean
    readonly color: number | undefined
    readonly rippleRadius: number | undefined
    readonly type: "RippleAndroid"
  }
  static SelectableBackground: (rippleRadius?: null | number | undefined) => {
    readonly attribute: "selectableItemBackground"
    readonly rippleRadius: number | undefined
    readonly type: "ThemeAttrAndroid"
  }
  static SelectableBackgroundBorderless: (
    rippleRadius?: null | number | undefined,
  ) => {
    readonly attribute: "selectableItemBackgroundBorderless"
    readonly rippleRadius: number | undefined
    readonly type: "ThemeAttrAndroid"
  }
  componentDidMount(): unknown
  componentDidUpdate(
    prevProps: TouchableNativeFeedbackProps,
    prevState: TouchableNativeFeedbackState,
  ): void
  componentWillUnmount(): void
  render(): React.ReactNode
}
declare type TouchableNativeFeedbackProps = Readonly<
  TouchableWithoutFeedbackProps &
    TVViewProps & {
      background?:
        | {
            readonly attribute:
              | "selectableItemBackground"
              | "selectableItemBackgroundBorderless"
            readonly rippleRadius: number | undefined
            readonly type: "ThemeAttrAndroid"
          }
        | {
            readonly borderless: boolean
            readonly color: number | undefined
            readonly rippleRadius: number | undefined
            readonly type: "RippleAndroid"
          }
      useForeground?: boolean
    }
>
declare type TouchableNativeFeedbackState = {
  readonly pressability: Pressability_default
}
declare type TouchableOpacity = typeof TouchableOpacity
declare type TouchableOpacityBaseProps = {
  readonly activeOpacity?: number
  readonly hostRef?: React.Ref<React.ComponentRef<typeof Animated.View>>
  readonly style?: Animated.WithAnimatedValue<ViewStyleProp>
}
declare type TouchableOpacityProps = Readonly<
  TouchableWithoutFeedbackProps & TVViewProps & TouchableOpacityBaseProps
>
declare type TouchableState =
  | typeof States.ERROR
  | typeof States.NOT_RESPONDER
  | typeof States.RESPONDER_ACTIVE_LONG_PRESS_IN
  | typeof States.RESPONDER_ACTIVE_LONG_PRESS_OUT
  | typeof States.RESPONDER_ACTIVE_PRESS_IN
  | typeof States.RESPONDER_ACTIVE_PRESS_OUT
  | typeof States.RESPONDER_INACTIVE_PRESS_IN
  | typeof States.RESPONDER_INACTIVE_PRESS_OUT
declare function TouchableWithoutFeedback(
  props: TouchableWithoutFeedbackProps,
): React.ReactNode
declare type TouchableWithoutFeedbackProps = Readonly<
  TouchableWithoutFeedbackPropsAndroid &
    TouchableWithoutFeedbackPropsIOS &
    AccessibilityProps & {
      children?: React.ReactNode
      delayLongPress?: number
      delayPressIn?: number
      delayPressOut?: number
      disabled?: boolean
      focusable?: boolean
      hitSlop?: EdgeInsetsOrSizeProp
      id?: string
      importantForAccessibility?: "auto" | "no-hide-descendants" | "no" | "yes"
      nativeID?: string
      onAccessibilityAction?: (event: AccessibilityActionEvent) => unknown
      onBlur?: (event: BlurEvent) => unknown
      onFocus?: (event: FocusEvent) => unknown
      onLayout?: (event: LayoutChangeEvent) => unknown
      onLongPress?: (event: GestureResponderEvent) => unknown
      onPress?: (event: GestureResponderEvent) => unknown
      onPressIn?: (event: GestureResponderEvent) => unknown
      onPressOut?: (event: GestureResponderEvent) => unknown
      pressRetentionOffset?: EdgeInsetsOrSizeProp
      rejectResponderTermination?: boolean
      style?: ViewStyleProp
      testID?: string
    }
>
declare type TouchableWithoutFeedbackPropsAndroid = {
  touchSoundDisabled?: boolean
}
declare type TouchableWithoutFeedbackPropsIOS = {
  tvParallaxProperties?: TVParallaxPropertiesType
}
declare type TouchEventProps = {
  readonly onTouchCancel?: (e: GestureResponderEvent) => void
  readonly onTouchCancelCapture?: (e: GestureResponderEvent) => void
  readonly onTouchEnd?: (e: GestureResponderEvent) => void
  readonly onTouchEndCapture?: (e: GestureResponderEvent) => void
  readonly onTouchMove?: (e: GestureResponderEvent) => void
  readonly onTouchMoveCapture?: (e: GestureResponderEvent) => void
  readonly onTouchStart?: (e: GestureResponderEvent) => void
  readonly onTouchStartCapture?: (e: GestureResponderEvent) => void
}
declare type TransformsStyle = ____TransformStyle_Internal
declare interface TurboModule extends DEPRECATED_RCTExport<void> {}
declare namespace TurboModuleRegistry {
  export { get_2 as get, getEnforcing }
}
declare type TVEventControl = typeof TVEventControl
declare type TVEventHandler = typeof TVEventHandler
declare type TVEventHandlerCallback = (event: any) => void
declare type TVEventHandlerType = {
  addListener: (callback: TVEventHandlerCallback) => EventSubscription
}
declare type TVFocusGuideView = typeof TVFocusGuideView
declare function TVFocusGuideView_default(
  $$PARAM_0$$: TVFocusGuideViewProps,
): React.ReactNode
declare type TVFocusGuideViewProps = Readonly<
  Omit<
    ViewProps,
    "autoFocus" | "destinations" | "enabled" | "focusable" | "safePadding"
  > & {
    autoFocus?: boolean
    destinations?: ComponentOrHandleType[]
    enabled?: boolean
    focusable?: boolean | void
    safePadding?: "both" | "horizontal" | "vertical" | null
    trapFocusDown?: boolean
    trapFocusLeft?: boolean
    trapFocusRight?: boolean
    trapFocusUp?: boolean
  }
>
declare type TVParallaxPropertiesType = {
  readonly enabled?: boolean
  readonly magnification?: number
  readonly pressDelay?: number
  readonly pressDuration?: number
  readonly pressMagnification?: number
  readonly shiftDistanceX?: number
  readonly shiftDistanceY?: number
  readonly tiltAngle?: number
}
declare type TVProps = {
  readonly hasTVPreferredFocus?: boolean
  readonly isTVSelectable?: boolean
  readonly nextFocusDown?: number
  readonly nextFocusForward?: number
  readonly nextFocusLeft?: number
  readonly nextFocusRight?: number
  readonly nextFocusUp?: number
  readonly onBlur?: (event: BlurEvent) => unknown
  readonly onBlurCapture?: (event: BlurEvent) => void
  readonly onFocus?: (event: FocusEvent) => unknown
  readonly onFocusCapture?: (event: FocusEvent) => void
  readonly tvParallaxProperties?: TVParallaxPropertiesType
}
declare type TVRemoteEvent = {
  readonly body?: any
  readonly eventKeyAction?: string
  readonly eventType: string
  readonly tag?: number
  readonly target?: number
}
declare class TVTextScrollView extends React.Component<
  Omit<
    Props,
    | "onBlur"
    | "onFocus"
    | "pageSize"
    | "scrollDuration"
    | "snapToEnd"
    | "snapToStart"
  > & {
    pageSize?: number
    scrollDuration?: number
    snapToEnd?: boolean
    snapToStart?: boolean
    onBlur?: (evt: Event) => void
    onFocus?: (evt: Event) => void
  }
> {
  componentDidMount(): void
  componentWillUnmount(): void
  render(): any
}
declare type TVViewProps = {
  readonly autoFocus?: boolean
  readonly destinations?: Object[]
  readonly enabled?: boolean
  readonly focusable?: boolean
  readonly hasTVPreferredFocus?: boolean
  readonly isTVSelectable?: boolean
  readonly nextFocusDown?: number
  readonly nextFocusForward?: number
  readonly nextFocusLeft?: number
  readonly nextFocusRight?: number
  readonly nextFocusUp?: number
  readonly safePadding?: null | string
  readonly tvParallaxProperties?: TVParallaxPropertiesType
  readonly onPressIn?: (event: any) => void
  readonly onPressOut?: (event: any) => void
}
declare type TVViewPropsIOS = Readonly<TVViewProps>
declare type UIManager = typeof UIManager
declare interface UIManagerJSInterface extends Spec_2 {
  readonly getViewManagerConfig: (viewManagerName: string) => Object
  readonly hasViewManagerConfig: (viewManagerName: string) => boolean
}
declare type unforkEvent = typeof unforkEvent
declare function unforkEventImpl(
  event: (AnimatedEvent | null | undefined) | (Function | null | undefined),
  listener: Function,
): void
declare function unmountApplicationComponentAtRootTag(rootTag: RootTag): void
declare type UnsafeEventObject = Object
declare type UnsafeMixed = unknown
declare type UnsafeNativeEventObject = Object
declare type UnsafeObject = Object
declare function useAnimatedValue(
  initialValue: number,
  config?: Animated.AnimatedConfig | null | undefined,
): Animated.Value
declare function useColorScheme(): ColorSchemeName | null | undefined
declare type useTVEventHandler = typeof useTVEventHandler
declare function useWindowDimensions(): DisplayMetrics | DisplayMetricsAndroid
declare type UTFSequence = typeof UTFSequence
declare type Value = null | {
  horizontal: boolean
}
declare type ValueOfUnion<T, K> = T extends any
  ? K extends keyof T
    ? T[K]
    : never
  : never
declare type ValueXYListenerCallback = (value: {
  x: number
  y: number
}) => unknown
declare type Vibration = typeof Vibration
declare type View = typeof View
declare function View_default(
  props: ViewProps & {
    ref?: React.Ref<React.ComponentRef<typeof $$ViewNativeComponent>>
  },
): React.ReactNode
declare type ViewabilityConfig = {
  readonly itemVisiblePercentThreshold?: number
  readonly minimumViewTime?: number
  readonly viewAreaCoveragePercentThreshold?: number
  readonly waitForInteraction?: boolean
}
declare type ViewabilityConfigCallbackPair = {
  viewabilityConfig: ViewabilityConfig
  onViewableItemsChanged: (info: {
    changed: Array<ListViewToken>
    viewableItems: Array<ListViewToken>
  }) => void
}
declare class ViewabilityHelper_default {
  computeViewableItems(
    props: CellMetricProps,
    scrollOffset: number,
    viewportHeight: number,
    listMetrics: ListMetricsAggregator_default,
    renderRange?: {
      first: number
      last: number
    },
  ): Array<number>
  constructor(config?: ViewabilityConfig)
  dispose(): void
  onUpdate(
    props: CellMetricProps,
    scrollOffset: number,
    viewportHeight: number,
    listMetrics: ListMetricsAggregator_default,
    createViewToken: (
      index: number,
      isViewable: boolean,
      props: CellMetricProps,
    ) => ListViewToken,
    onViewableItemsChanged: ($$PARAM_0$$: {
      changed: Array<ListViewToken>
      viewableItems: Array<ListViewToken>
    }) => void,
    renderRange?: {
      first: number
      last: number
    },
  ): void
  recordInteraction(): void
  resetViewableIndices(): void
}
declare type ViewabilityHelperT = typeof ViewabilityHelper_default
declare type ViewBaseProps = {
  readonly children?: React.ReactNode
  readonly collapsable?: boolean
  readonly collapsableChildren?: boolean
  readonly hitSlop?: EdgeInsetsOrSizeProp
  readonly id?: string
  readonly nativeID?: string
  readonly needsOffscreenAlphaCompositing?: boolean
  readonly pointerEvents?: "auto" | "box-none" | "box-only" | "none"
  readonly removeClippedSubviews?: boolean
  readonly style?: ViewStyleProp
  readonly testID?: string
}
declare type ViewConfig = {
  readonly baseModuleName?: string
  readonly bubblingEventTypes?: {
    readonly [eventName: string]: {
      readonly phasedRegistrationNames: {
        readonly bubbled: string
        readonly captured: string
        readonly skipBubbling?: boolean
      }
    }
  }
  readonly Commands?: {
    readonly [commandName: string]: number
  }
  readonly Constants?: {
    readonly [name: string]: unknown
  }
  readonly directEventTypes?: {
    readonly [eventName: string]: {
      readonly registrationName: string
    }
  }
  readonly Manager?: string
  readonly NativeProps?: {
    readonly [propName: string]: string
  }
  readonly supportsRawText?: boolean
  readonly uiViewClassName: string
  readonly validAttributes: AttributeConfiguration
}
declare type ViewProps = Readonly<
  DirectEventProps &
    GestureResponderHandlers &
    MouseEventProps &
    PointerEventProps &
    FocusEventProps &
    TouchEventProps &
    ViewPropsAndroid &
    ViewPropsIOS &
    AccessibilityProps &
    ViewBaseProps &
    TVViewProps &
    PressEventProps
>
declare type ViewPropsAndroid = {
  readonly nativeBackgroundAndroid?: AndroidDrawable
  readonly nativeForegroundAndroid?: AndroidDrawable
  readonly onClick?: (event: GestureResponderEvent) => unknown
  readonly renderToHardwareTextureAndroid?: boolean
  readonly tabIndex?: -1 | 0
}
declare type ViewPropsIOS = {
  readonly shouldRasterizeIOS?: boolean
}
declare type ViewStyle = ____ViewStyle_Internal
declare type ViewStyleProp = ____ViewStyleProp_Internal
declare type VirtualizedList = typeof VirtualizedList
declare class VirtualizedList_default extends StateSafePureComponent_default<
  VirtualizedListProps,
  State
> {
  static contextType: typeof VirtualizedListContext
  state: State
  componentDidMount(): void
  componentDidUpdate(prevProps: VirtualizedListProps): void
  componentWillUnmount(): void
  constructor(props: VirtualizedListProps)
  flashScrollIndicators(): void
  static getDerivedStateFromProps(
    newProps: VirtualizedListProps,
    prevState: State,
  ): State
  getScrollableNode(): null | number | undefined
  getScrollRef(): null | React.ComponentRef<typeof ScrollView> | undefined
  getScrollResponder(): null | ScrollResponderType | undefined
  hasMore(): boolean
  measureLayoutRelativeToContainingList(): void
  recordInteraction(): void
  render(): React.ReactNode
  scrollToEnd(
    params?:
      | null
      | undefined
      | {
          animated?: boolean
        },
  ): void
  scrollToIndex(params: {
    animated?: boolean
    index: number
    viewOffset?: number
    viewPosition?: number
  }): any
  scrollToItem(params: {
    animated?: boolean
    item: Item
    viewOffset?: number
    viewPosition?: number
  }): void
  scrollToOffset(params: { animated?: boolean; offset: number }): void
  setNativeProps(props: Object): void
}
declare type VirtualizedListContext = typeof VirtualizedListContext
declare function VirtualizedListContextResetter($$PARAM_0$$: {
  children: React.ReactNode
}): React.ReactNode
declare type VirtualizedListContextResetterT =
  typeof VirtualizedListContextResetter
declare type VirtualizedListProps = ScrollViewProps &
  RequiredVirtualizedListProps &
  OptionalVirtualizedListProps
declare type VirtualizedListT = typeof VirtualizedList_default
declare type VirtualizedListType = typeof $$index.VirtualizedList
declare type VirtualizedSectionList = typeof VirtualizedSectionList
declare type VirtualizedSectionListProps<
  ItemT,
  SectionT = DefaultVirtualizedSectionT,
> = RequiredVirtualizedSectionListProps<ItemT, SectionT> &
  OptionalVirtualizedSectionListProps<ItemT, SectionT> &
  Omit<VirtualizedListProps, "data" | "renderItem">
declare type VirtualizedSectionListType = typeof $$index.VirtualizedSectionList
declare enum VirtualViewMode {
  Hidden = 2,
  Prerender = 1,
  Visible = 0,
}
declare namespace VirtualViewMode {
  export function cast(value: null | number | undefined): VirtualViewMode
  export function isValid(
    value: null | number | undefined,
  ): value is VirtualViewMode
  export function members(): IterableIterator<VirtualViewMode>
  export function getName(value: VirtualViewMode): string
}
declare type WebPlatform = {
  OS: "web"
  select: <T>(spec: PlatformSelectSpec<T>) => T
  get constants(): {
    reactNativeVersion: {
      major: number
      minor: number
      patch: number
      prerelease: string | undefined
    }
  }
  get isDisableAnimations(): boolean
  get isTesting(): boolean
  get isTV(): boolean
  get Version(): string
}
declare type WindowsPlatform = {
  OS: "windows"
  select: <T>(spec: PlatformSelectSpec<T>) => T
  get constants(): {
    isDisableAnimations?: boolean
    isTesting: boolean
    osVersion: number
    reactNativeVersion: {
      major: number
      minor: number
      patch: number
      prerelease: string | undefined
    }
    reactNativeWindowsVersion: {
      major: number
      minor: number
      patch: number
    }
  }
  get isDisableAnimations(): boolean
  get isTesting(): boolean
  get isTV(): boolean
  get Version(): number
}
declare type WithAnimatedValue<T> = T extends Builtin | Nullable
  ? T
  : T extends Primitive
    ?
        | AnimatedAddition_default
        | AnimatedDiffClamp_default
        | AnimatedDivision_default
        | AnimatedInterpolation_default<number | string>
        | AnimatedInterpolation_default<number>
        | AnimatedInterpolation_default<string>
        | AnimatedModulo_default
        | AnimatedMultiplication_default
        | AnimatedNode_default
        | AnimatedSubtraction_default
        | AnimatedValue_default
        | T
    : T extends ReadonlyArray<infer P>
      ? ReadonlyArray<WithAnimatedValue<P>>
      : T extends {}
        ? { readonly [K in keyof T]: WithAnimatedValue<T[K]> }
        : T
declare type WithDefault<
  Type extends DefaultTypes,
  Value extends (null | Type | undefined) | string,
> = null | Type | undefined
declare function Wrapper_default(
  $$PARAM_0$$: ModalRefProps & ModalProps,
): React.ReactNode
declare type WrapperComponentProvider = (
  appParameters: Object,
) => React.ComponentType<any>
export {
  AccessibilityActionEvent, // f6181a2c
  AccessibilityInfo, // 70604904
  AccessibilityProps, // 5a2836fc
  AccessibilityRole, // f2f2e066
  AccessibilityState, // b0c2b3f7
  AccessibilityValue, // cf8bcb74
  ActionSheetIOS, // 88e6bfb0
  ActionSheetIOSOptions, // 1756eb5a
  ActivityIndicator, // 8d041a45
  ActivityIndicatorProps, // cc0f004e
  Alert, // 5bf12165
  AlertButton, // bf1a3b60
  AlertButtonStyle, // ec9fb242
  AlertOptions, // a0cdac0f
  AlertType, // 5ab91217
  AndroidKeyboardEvent, // e03becc8
  Animated, // 634d506f
  AppConfig, // ebddad4b
  AppRegistry, // 6cdee1d6
  AppState, // f7097b1b
  AppStateEvent, // 80f034c3
  AppStateStatus, // 447e5ef2
  Appearance, // 00cbaa0a
  AutoCapitalize, // c0e857a0
  BackHandler, // a9f9bad9
  BackPressEventName, // 4620fb76
  BlurEvent, // 870b9bb5
  BoxShadowValue, // b679703f
  Button, // dd130b61
  ButtonProps, // 3c081e75
  Clipboard, // 9b8c878e
  CodegenTypes, // 030a94b8
  ColorSchemeName, // 31a4350e
  ColorValue, // 98989a8f
  ComponentProvider, // b5c60ddd
  ComponentProviderInstrumentationHook, // 9f640048
  CursorValue, // 26522595
  DevMenu, // 99e9fcd6
  DevSettings, // 1a2f3a5f
  DeviceEventEmitter, // 31dc96e7
  DeviceInfo, // cc37eaec
  DeviceInfoConstants, // 279e7858
  DimensionValue, // b163a381
  Dimensions, // 980ef68c
  DimensionsPayload, // 653bc26c
  DisplayMetrics, // 1dc35cef
  DisplayMetricsAndroid, // 872e62eb
  DrawerLayoutAndroid, // 14121b61
  DrawerLayoutAndroidProps, // 90143e47
  DrawerSlideEvent, // cc43db83
  DropShadowValue, // e9df2606
  DynamicColorIOS, // 1f9b3410
  DynamicColorIOSTuple, // 023ce58e
  Easing, // b624f91d
  EasingFunction, // 14aee4c0
  EdgeInsetsValue, // bd44afe6
  EmitterSubscription, // de50c359
  EnterKeyHintTypeOptions, // 8314de78
  ErrorUtils, // 38fb909e
  EventSubscription, // b8d084aa
  ExtendedExceptionData, // 5a6ccf5a
  FilterFunction, // bf24c0e3
  FlatList, // 0c06542e
  FlatListProps, // 041b9b10
  FocusEvent, // 529b43eb
  FontVariant, // 7c7558bb
  GestureResponderEvent, // b466f6d6
  GestureResponderHandlers, // 8356843d
  Handle, // 2d65285d
  HostComponent, // 5e13ff5a
  HostInstance, // 489cbe7f
  I18nManager, // f2fa58ce
  IOSKeyboardEvent, // e67bfe3a
  IgnorePattern, // ec6f6ece
  Image, // 04474205
  ImageBackground, // 10b3932b
  ImageBackgroundProps, // 9b18e63e
  ImageErrorEvent, // b7b2ae63
  ImageLoadEvent, // 5baae813
  ImageProgressEventIOS, // adb35052
  ImageProps, // de3580a4
  ImagePropsAndroid, // 9fd9bcbb
  ImagePropsBase, // 9b60ebb5
  ImagePropsIOS, // 318adce2
  ImageRequireSource, // 681d683b
  ImageResolvedAssetSource, // f3060931
  ImageSize, // 1c47cf88
  ImageSource, // 48c7f316
  ImageSourcePropType, // bfb5e5c6
  ImageStyle, // 8b22ac76
  ImageURISource, // 016eb083
  InputAccessoryView, // 591855d8
  InputAccessoryViewProps, // 4b6f5450
  InputModeOptions, // 4e8581b9
  Insets, // e7fe432a
  InteractionManager, // 301bfa63
  Keyboard, // 87311c77
  KeyboardAvoidingView, // dd7a3ad8
  KeyboardAvoidingViewProps, // 2a895711
  KeyboardEvent, // c3f895d4
  KeyboardEventEasing, // af4091c8
  KeyboardEventName, // 59299ad6
  KeyboardMetrics, // 7997b799
  KeyboardTypeOptions, // ae44efe7
  LayoutAnimation, // 795a5df3
  LayoutAnimationAnim, // 074da826
  LayoutAnimationConfig, // 5e200571
  LayoutAnimationProperties, // 84e6e197
  LayoutAnimationProperty, // 52995f01
  LayoutAnimationType, // 2da0a29b
  LayoutAnimationTypes, // 081b3bde
  LayoutChangeEvent, // c674f902
  LayoutConformanceProps, // 055f03b8
  LayoutRectangle, // 6601b294
  Linking, // 292de0a0
  ListRenderItem, // b5353fd8
  ListRenderItemInfo, // e8595b03
  ListViewToken, // 833d3481
  LogBox, // b58880c6
  LogData, // 89af6d4c
  MeasureInWindowOnSuccessCallback, // a285f598
  MeasureLayoutOnSuccessCallback, // 3592502a
  MeasureOnSuccessCallback, // 82824e59
  Modal, // 78e8a79d
  ModalBaseProps, // 0c81c9b1
  ModalProps, // 4a435290
  ModalPropsAndroid, // 515fb173
  ModalPropsIOS, // 4fbcedf6
  ModeChangeEvent, // b889a7ce
  MouseEvent, // 53ede3db
  NativeAppEventEmitter, // b4d20c1d
  NativeColorValue, // d2094c29
  NativeComponentRegistry, // 7fd99ba6
  NativeDialogManagerAndroid, // 6254873e
  NativeEventEmitter, // d72906cc
  NativeEventSubscription, // de3942e7
  NativeMethods, // 03dc51c5
  NativeMethodsMixin, // 4b061b7e
  NativeModules, // 1cf72876
  NativeMouseEvent, // ff25cf35
  NativePointerEvent, // 89c1f3ad
  NativeScrollEvent, // caad7f53
  NativeSyntheticEvent, // d2a1fe6a
  NativeTouchEvent, // 59b676df
  NativeUIEvent, // 44ac26ac
  Networking, // b674447b
  OpaqueColorValue, // 25f3fa5b
  PanResponder, // 98a9b6fc
  PanResponderCallbacks, // d325aa56
  PanResponderGestureState, // 54baf558
  PanResponderInstance, // c8b0d00c
  Permission, // 06473f4f
  PermissionStatus, // 4b7de97b
  PermissionsAndroid, // 0c1619e7
  PixelRatio, // 10d9e32d
  Platform, // dc0f5740
  PlatformColor, // be964947
  PlatformOSType, // 0a17561e
  PlatformSelectSpec, // 09ed7758
  PointValue, // 69db075f
  PointerEvent, // ff3129ff
  Pressable, // 3c6e4eb9
  PressableAndroidRippleConfig, // 42bc9727
  PressableProps, // 97144bb3
  PressableStateCallbackType, // 245077a8
  ProcessedColorValue, // 33f74304
  ProgressBarAndroid, // 03e66cf5
  ProgressBarAndroidProps, // a201d1c8
  PromiseTask, // 5102c862
  PublicRootInstance, // 8040afd7
  PublicTextInstance, // 7d73f802
  PushNotificationEventName, // 84e7e150
  PushNotificationIOS, // b4d1fe78
  PushNotificationPermissions, // c2e7ae4f
  Rationale, // 5df1b1c1
  ReactNativeVersion, // abd76827
  RefreshControl, // 1786c0e1
  RefreshControlProps, // 7a962b30
  RefreshControlPropsAndroid, // 99f64c97
  RefreshControlPropsIOS, // 72a36381
  Registry, // e1ed403e
  ResponderSyntheticEvent, // e0d1564d
  ReturnKeyTypeOptions, // afd47ba3
  Role, // af7b889d
  RootTag, // 3cd10504
  RootTagContext, // 15b60335
  RootViewStyleProvider, // cc8d50e9
  Runnable, // 2cb32c54
  Runnables, // d3749ae1
  SafeAreaView, // 4364c7bb
  ScaledSize, // 07e417c7
  ScrollEvent, // 84e5b805
  ScrollResponderType, // d39056e7
  ScrollToLocationParamsType, // d7ecdad1
  ScrollView, // 7fb7c469
  ScrollViewImperativeMethods, // eb20aa46
  ScrollViewProps, // f6fc28f1
  ScrollViewPropsAndroid, // 84e2134b
  ScrollViewPropsIOS, // 089722ca
  ScrollViewScrollToOptions, // 3313411e
  SectionBase, // b376bddc
  SectionList, // 39be3483
  SectionListData, // 119baf83
  SectionListProps, // 6deb8ac9
  SectionListRenderItem, // 1fad0435
  SectionListRenderItemInfo, // 745e1992
  Separators, // 6a45f7e3
  Settings, // 4282b0da
  Share, // e4591b32
  ShareAction, // ead1004a
  ShareActionSheetError, // 55e4f451
  ShareActionSheetIOSOptions, // eff574f5
  ShareContent, // 7c627896
  ShareOptions, // 800c3a4e
  SimpleTask, // 0e619d11
  StatusBar, // 5e08d563
  StatusBarAnimation, // 7fd047e6
  StatusBarProps, // 06c98add
  StatusBarStyle, // 986b2051
  StyleProp, // fa0e9b4a
  StyleSheet, // 366689d4
  SubmitBehavior, // c4ddf490
  Switch, // aebc9941
  SwitchChangeEvent, // 2e5bd2de
  SwitchProps, // 355b592b
  Systrace, // b5aa21fc
  TVEventControl, // 71f1bd97
  TVEventHandler, // 39fe3004
  TVFocusGuideView, // c77de205
  TVRemoteEvent, // 920bc385
  TVTextScrollView, // 9fd99901
  TVViewPropsIOS, // 36d340bd
  TargetedEvent, // 16e98910
  TaskProvider, // 266dedf2
  Text, // bd525aa9
  TextContentType, // 239b3ecc
  TextInput, // 282b394e
  TextInputAndroidProps, // 3f09ce49
  TextInputChangeEvent, // 380cbe93
  TextInputContentSizeChangeEvent, // 5fba3f54
  TextInputEndEditingEvent, // 8c22fac3
  TextInputFocusEvent, // c36e977c
  TextInputIOSProps, // 0d05a855
  TextInputKeyPressEvent, // 967178c2
  TextInputProps, // f32ef684
  TextInputSelectionChangeEvent, // a1a7622f
  TextInputSubmitEditingEvent, // 48d903af
  TextLayoutEvent, // 45b0a8d7
  TextProps, // 95d8874d
  TextStyle, // f3404e2b
  ToastAndroid, // b4875e35
  Touchable, // 93eb6c63
  TouchableHighlight, // b4304a98
  TouchableHighlightProps, // 9815f649
  TouchableNativeFeedback, // 3fcde333
  TouchableNativeFeedbackProps, // ba38a445
  TouchableOpacity, // 7e33acfd
  TouchableOpacityProps, // 3a9c0765
  TouchableWithoutFeedback, // 5bb1984d
  TouchableWithoutFeedbackProps, // 83e47542
  TransformsStyle, // 65e70f18
  TurboModule, // dfe29706
  TurboModuleRegistry, // 4ace6db2
  UIManager, // 8d2c8281
  UTFSequence, // baacd11b
  Vibration, // 315e131d
  View, // 39dd4de4
  ViewProps, // 0220c444
  ViewPropsAndroid, // 75865e4a
  ViewPropsIOS, // 58ee19bf
  ViewStyle, // c2db0e6e
  VirtualViewMode, // 85a69ef6
  VirtualizedList, // 4d513939
  VirtualizedListProps, // bea31fd6
  VirtualizedSectionList, // 446ba0df
  VirtualizedSectionListProps, // 5fb51092
  WrapperComponentProvider, // 9cf3844c
  codegenNativeCommands, // e16d62f7
  codegenNativeComponent, // ed4c8103
  findNodeHandle, // c6b08494
  processColor, // 00453092
  registerCallableModule, // 86b65226
  requireNativeComponent, // e659aa32
  useAnimatedValue, // 2c80bbc2
  useColorScheme, // c216d6f7
  useTVEventHandler, // 1e75048d
  useWindowDimensions, // bb4b683f
}
