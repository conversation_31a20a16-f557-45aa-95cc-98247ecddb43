// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LogBoxInspectorStackFrames should render null for empty stack frames 1`] = `null`;

exports[`LogBoxInspectorStackFrames should render stack frames with 1 frame collapsed 1`] = `
<View
  style={
    Object {
      "marginTop": 15,
    }
  }
>
  <View
    style={
      Object {
        "alignItems": "center",
        "flexDirection": "row",
        "marginBottom": 10,
        "paddingHorizontal": 12,
      }
    }
  >
    <Text
      style={
        Object {
          "color": "rgba(255, 255, 255, 1)",
          "flex": 1,
          "fontSize": 18,
          "fontWeight": "600",
          "includeFontPadding": false,
          "lineHeight": 20,
        }
      }
    >
      Call Stack
    </Text>
  </View>
  <View
    style={
      Object {
        "paddingBottom": 10,
      }
    }
  >
    <View
      style={
        Object {
          "backgroundColor": "rgba(51, 51, 51, 1)",
          "borderRadius": 5,
          "marginBottom": 5,
          "marginHorizontal": 10,
          "paddingHorizontal": 5,
          "paddingVertical": 10,
        }
      }
    >
      <Text
        style={
          Object {
            "color": "rgba(255, 255, 255, 0.7)",
            "fontSize": 13,
            "fontWeight": "400",
            "includeFontPadding": false,
            "lineHeight": 18,
            "marginHorizontal": 10,
          }
        }
      >
        This call stack is not symbolicated. Some features are unavailable such as viewing the function name or tapping to open files.
      </Text>
    </View>
    <View
      style={
        Object {
          "flexDirection": "row",
          "paddingHorizontal": 15,
        }
      }
    >
      <View
        style={
          Array [
            Object {
              "backgroundColor": "transparent",
            },
            Object {
              "borderRadius": 5,
              "flex": 1,
              "paddingHorizontal": 10,
              "paddingVertical": 4,
            },
          ]
        }
      >
        <Text
          id="logbox_stack_frame_text"
          style={
            Array [
              Object {
                "color": "rgba(255, 255, 255, 1)",
                "fontFamily": "Menlo",
                "fontSize": 14,
                "fontWeight": "400",
                "includeFontPadding": false,
                "lineHeight": 18,
              },
              false,
            ]
          }
        >
          foo
        </Text>
        <Text
          ellipsizeMode="middle"
          numberOfLines={1}
          style={
            Array [
              Object {
                "color": "rgba(255, 255, 255, 0.8)",
                "fontSize": 12,
                "fontWeight": "300",
                "includeFontPadding": false,
                "lineHeight": 16,
                "paddingLeft": 10,
              },
              false,
            ]
          }
        >
          dependency.js:1:2
        </Text>
      </View>
    </View>
    <View
      style={
        Object {
          "flexDirection": "row",
          "marginLeft": 15,
        }
      }
    >
      <View
        accessibilityState={
          Object {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessible={true}
        focusable={true}
        onClick={[Function]}
        onPressIn={[Function]}
        onPressOut={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          Array [
            Object {
              "backgroundColor": "transparent",
            },
            Object {
              "borderRadius": 5,
            },
          ]
        }
      >
        <Text
          style={
            Object {
              "color": "rgba(255, 255, 255, 0.7)",
              "fontSize": 12,
              "fontWeight": "300",
              "lineHeight": 20,
              "marginTop": 0,
              "paddingHorizontal": 10,
              "paddingVertical": 5,
            }
          }
        >
          See 1 more frame
        </Text>
      </View>
    </View>
  </View>
</View>
`;
