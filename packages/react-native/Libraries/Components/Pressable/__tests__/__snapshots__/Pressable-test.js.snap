// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Pressable /> should render as expected: should deep render when mocked (please verify output manually) 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`<Pressable /> should render as expected: should deep render when not mocked (please verify output manually) 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`<Pressable disabled={true} /> should be disabled when disabled is true: should deep render when mocked (please verify output manually) 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`<Pressable disabled={true} /> should be disabled when disabled is true: should deep render when not mocked (please verify output manually) 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`<Pressable disabled={true} accessibilityState={{}} /> should be disabled when disabled is true and accessibilityState is empty: should deep render when mocked (please verify output manually) 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`<Pressable disabled={true} accessibilityState={{}} /> should be disabled when disabled is true and accessibilityState is empty: should deep render when not mocked (please verify output manually) 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`<Pressable disabled={true} accessibilityState={{checked: true}} /> should keep accessibilityState when disabled is true: should deep render when mocked (please verify output manually) 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": true,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`<Pressable disabled={true} accessibilityState={{checked: true}} /> should keep accessibilityState when disabled is true: should deep render when not mocked (please verify output manually) 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": true,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`<Pressable disabled={true} accessibilityState={{disabled: false}} /> should overwrite accessibilityState with value of disabled prop: should deep render when mocked (please verify output manually) 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`<Pressable disabled={true} accessibilityState={{disabled: false}} /> should overwrite accessibilityState with value of disabled prop: should deep render when not mocked (please verify output manually) 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;
