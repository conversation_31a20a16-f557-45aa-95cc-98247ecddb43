// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TouchableOpacity renders correctly 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "opacity": 1,
    }
  }
>
  <Text>
    Touchable
  </Text>
</View>
`;

exports[`TouchableOpacity renders in disabled state when a disabled prop is passed 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "opacity": 1,
    }
  }
>
  <Text>
    Touchable
  </Text>
</View>
`;

exports[`TouchableOpacity renders in disabled state when a key disabled in accessibilityState is passed 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "opacity": 1,
    }
  }
>
  <Text>
    Touchable
  </Text>
</View>
`;
