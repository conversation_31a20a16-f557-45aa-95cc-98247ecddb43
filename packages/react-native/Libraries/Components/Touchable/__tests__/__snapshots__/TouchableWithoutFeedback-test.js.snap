// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TouchableWithoutFeedback renders correctly 1`] = `
<Text
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessible={true}
  focusable={false}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  Touchable
</Text>
`;

exports[`TouchableWithoutFeedback with disabled state should be disabled when disabled is true 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessible={true}
  focusable={false}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
/>
`;

exports[`TouchableWithoutFeedback with disabled state should be disabled when disabled is true and accessibilityState is empty 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessible={true}
  focusable={false}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
/>
`;

exports[`TouchableWithoutFeedback with disabled state should disable button when accessibilityState is disabled 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessible={true}
  focusable={false}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
/>
`;

exports[`TouchableWithoutFeedback with disabled state should keep accessibilityState when disabled is true 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": true,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessible={true}
  focusable={false}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
/>
`;

exports[`TouchableWithoutFeedback with disabled state should overwrite accessibilityState with value of disabled prop 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessible={true}
  focusable={false}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
/>
`;
