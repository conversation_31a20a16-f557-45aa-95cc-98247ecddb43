// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TouchableHighlight renders correctly 1`] = `
<View
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={Object {}}
>
  <Text>
    Touchable
  </Text>
</View>
`;

exports[`TouchableHighlight with disabled state should be disabled when disabled is true 1`] = `
<View
  accessibilityState={
    Object {
      "disabled": true,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`TouchableHighlight with disabled state should be disabled when disabled is true and accessibilityState is empty 1`] = `
<View
  accessibilityState={
    Object {
      "disabled": true,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`TouchableHighlight with disabled state should disable button when accessibilityState is disabled 1`] = `
<View
  accessibilityState={
    Object {
      "disabled": true,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`TouchableHighlight with disabled state should keep accessibilityState when disabled is true 1`] = `
<View
  accessibilityState={
    Object {
      "checked": true,
      "disabled": true,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;

exports[`TouchableHighlight with disabled state should overwrite accessibilityState with value of disabled prop 1`] = `
<View
  accessibilityState={
    Object {
      "disabled": true,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  <View />
</View>
`;
