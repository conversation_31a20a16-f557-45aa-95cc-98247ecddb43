// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<TouchableNativeFeedback /> should render as expected 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
/>
`;

exports[`<TouchableNativeFeedback disabled={false} accessibilityState={{disabled:true}}> should overwrite accessibilityState with value of disabled prop 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": false,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
/>
`;

exports[`<TouchableNativeFeedback disabled={true} accessibilityState={{}}> should be disabled when disabled is true and accessibilityState is empty 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
/>
`;

exports[`<TouchableNativeFeedback disabled={true} accessibilityState={{checked: true}}> should keep accessibilityState when disabled is true 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": true,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
/>
`;

exports[`<TouchableNativeFeedback disabled={true} accessibilityState={{disabled:false}}> should overwrite accessibilityState with value of disabled prop 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
/>
`;

exports[`<TouchableNativeFeedback disabled={true}> should be disabled when disabled is true 1`] = `
<View
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
/>
`;

exports[`TouchableWithoutFeedback renders correctly 1`] = `
<Text
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  focusable={true}
  onClick={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
>
  Touchable
</Text>
`;
