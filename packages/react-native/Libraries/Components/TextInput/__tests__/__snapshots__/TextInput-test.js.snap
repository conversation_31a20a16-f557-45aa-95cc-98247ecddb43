// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TextInput tests (useTextChildren = false) should render as expected: should deep render when mocked (please verify output manually) 1`] = `
<RCTSinglelineTextInputView
  accessible={true}
  allowFontScaling={true}
  focusable={true}
  mostRecentEventCount={0}
  onBlur={[Function]}
  onChange={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onScroll={[Function]}
  onSelectionChange={[Function]}
  onSelectionChangeShouldSetResponder={[Function]}
  onStartShouldSetResponder={[Function]}
  rejectResponderTermination={true}
  selection={null}
  submitBehavior="blurAndSubmit"
  underlineColorAndroid="transparent"
/>
`;

exports[`TextInput tests (useTextChildren = false) should render as expected: should deep render when not mocked (please verify output manually) 1`] = `
<RCTSinglelineTextInputView
  accessible={true}
  allowFontScaling={true}
  focusable={true}
  mostRecentEventCount={0}
  onBlur={[Function]}
  onChange={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onScroll={[Function]}
  onSelectionChange={[Function]}
  onSelectionChangeShouldSetResponder={[Function]}
  onStartShouldSetResponder={[Function]}
  rejectResponderTermination={true}
  selection={null}
  submitBehavior="blurAndSubmit"
  underlineColorAndroid="transparent"
/>
`;

exports[`TextInput tests (useTextChildren = true) should render as expected: should deep render when mocked (please verify output manually) 1`] = `
<RCTSinglelineTextInputView
  accessible={true}
  allowFontScaling={true}
  focusable={true}
  mostRecentEventCount={0}
  onBlur={[Function]}
  onChange={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onScroll={[Function]}
  onSelectionChange={[Function]}
  onSelectionChangeShouldSetResponder={[Function]}
  onStartShouldSetResponder={[Function]}
  rejectResponderTermination={true}
  selection={null}
  submitBehavior="blurAndSubmit"
  underlineColorAndroid="transparent"
/>
`;

exports[`TextInput tests (useTextChildren = true) should render as expected: should deep render when not mocked (please verify output manually) 1`] = `
<RCTSinglelineTextInputView
  accessible={true}
  allowFontScaling={true}
  focusable={true}
  mostRecentEventCount={0}
  onBlur={[Function]}
  onChange={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onScroll={[Function]}
  onSelectionChange={[Function]}
  onSelectionChangeShouldSetResponder={[Function]}
  onStartShouldSetResponder={[Function]}
  rejectResponderTermination={true}
  selection={null}
  submitBehavior="blurAndSubmit"
  underlineColorAndroid="transparent"
/>
`;
