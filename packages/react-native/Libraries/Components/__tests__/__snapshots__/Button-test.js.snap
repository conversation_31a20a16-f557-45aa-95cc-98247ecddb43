// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Button /> should be disabled and it should set accessibilityState to disabled when disabled={true} 1`] = `
<View
  accessibilityRole="button"
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "opacity": 1,
    }
  }
>
  <View
    style={
      Array [
        Object {},
        Object {},
      ]
    }
  >
    <Text
      disabled={true}
      style={
        Array [
          Object {
            "color": "#007AFF",
            "fontSize": 18,
            "margin": 8,
            "textAlign": "center",
          },
          Object {
            "color": "#cdcdcd",
          },
        ]
      }
    >
      Test Button
    </Text>
  </View>
</View>
`;

exports[`<Button /> should be disabled when disabled is empty and accessibilityState={{disabled: true}} 1`] = `
<View
  accessibilityRole="button"
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "opacity": 1,
    }
  }
>
  <View
    style={
      Array [
        Object {},
        Object {},
      ]
    }
  >
    <Text
      disabled={true}
      style={
        Array [
          Object {
            "color": "#007AFF",
            "fontSize": 18,
            "margin": 8,
            "textAlign": "center",
          },
          Object {
            "color": "#cdcdcd",
          },
        ]
      }
    >
      Test Button
    </Text>
  </View>
</View>
`;

exports[`<Button /> should be disabled when disabled={true} and accessibilityState={{disabled: true}} 1`] = `
<View
  accessibilityRole="button"
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "opacity": 1,
    }
  }
>
  <View
    style={
      Array [
        Object {},
        Object {},
      ]
    }
  >
    <Text
      disabled={true}
      style={
        Array [
          Object {
            "color": "#007AFF",
            "fontSize": 18,
            "margin": 8,
            "textAlign": "center",
          },
          Object {
            "color": "#cdcdcd",
          },
        ]
      }
    >
      Test Button
    </Text>
  </View>
</View>
`;

exports[`<Button /> should be set importantForAccessibility={no-hide-descendants} when importantForAccessibility={no} 1`] = `
<View
  accessibilityRole="button"
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  hasTVPreferredFocus={false}
  importantForAccessibility="no-hide-descendants"
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "opacity": 1,
    }
  }
>
  <View
    style={
      Array [
        Object {},
      ]
    }
  >
    <Text
      style={
        Array [
          Object {
            "color": "#007AFF",
            "fontSize": 18,
            "margin": 8,
            "textAlign": "center",
          },
        ]
      }
    >
      Test Button
    </Text>
  </View>
</View>
`;

exports[`<Button /> should be set importantForAccessibility={no-hide-descendants} when importantForAccessibility={no-hide-descendants} 1`] = `
<View
  accessibilityRole="button"
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  hasTVPreferredFocus={false}
  importantForAccessibility="no-hide-descendants"
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "opacity": 1,
    }
  }
>
  <View
    style={
      Array [
        Object {},
      ]
    }
  >
    <Text
      style={
        Array [
          Object {
            "color": "#007AFF",
            "fontSize": 18,
            "margin": 8,
            "textAlign": "center",
          },
        ]
      }
    >
      Test Button
    </Text>
  </View>
</View>
`;

exports[`<Button /> should not be disabled when disabled={false} and accessibilityState={{disabled: false}} 1`] = `
<View
  accessibilityRole="button"
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": false,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "opacity": 1,
    }
  }
>
  <View
    style={
      Array [
        Object {},
      ]
    }
  >
    <Text
      disabled={false}
      style={
        Array [
          Object {
            "color": "#007AFF",
            "fontSize": 18,
            "margin": 8,
            "textAlign": "center",
          },
        ]
      }
    >
      Test Button
    </Text>
  </View>
</View>
`;

exports[`<Button /> should not be disabled when disabled={false} and accessibilityState={{disabled: true}} 1`] = `
<View
  accessibilityRole="button"
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": false,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "opacity": 1,
    }
  }
>
  <View
    style={
      Array [
        Object {},
      ]
    }
  >
    <Text
      disabled={false}
      style={
        Array [
          Object {
            "color": "#007AFF",
            "fontSize": 18,
            "margin": 8,
            "textAlign": "center",
          },
        ]
      }
    >
      Test Button
    </Text>
  </View>
</View>
`;

exports[`<Button /> should overwrite accessibilityState with value of disabled prop 1`] = `
<View
  accessibilityRole="button"
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": true,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "opacity": 1,
    }
  }
>
  <View
    style={
      Array [
        Object {},
        Object {},
      ]
    }
  >
    <Text
      disabled={true}
      style={
        Array [
          Object {
            "color": "#007AFF",
            "fontSize": 18,
            "margin": 8,
            "textAlign": "center",
          },
          Object {
            "color": "#cdcdcd",
          },
        ]
      }
    >
      Test Button
    </Text>
  </View>
</View>
`;

exports[`<Button /> should render as expected 1`] = `
<View
  accessibilityRole="button"
  accessibilityState={
    Object {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    Object {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  hasTVPreferredFocus={false}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onPressIn={[Function]}
  onPressOut={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    Object {
      "opacity": 1,
    }
  }
>
  <View
    style={
      Array [
        Object {},
      ]
    }
  >
    <Text
      style={
        Array [
          Object {
            "color": "#007AFF",
            "fontSize": 18,
            "margin": 8,
            "textAlign": "center",
          },
        ]
      }
    >
      Test Button
    </Text>
  </View>
</View>
`;
